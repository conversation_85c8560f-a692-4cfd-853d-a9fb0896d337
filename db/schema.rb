# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_15_120000) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"
  enable_extension "pg_stat_statements"
  enable_extension "unaccent"

  create_table "alerts", id: :serial, force: :cascade do |t|
    t.integer "category", default: 0
    t.datetime "email_sent_at", precision: nil
    t.text "meta", default: "{}"
    t.text "message"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "api_oauth_managers", id: :serial, force: :cascade do |t|
    t.string "access_token_url"
    t.string "access_token"
    t.string "login_name"
    t.string "passphrase"
    t.integer "api_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "api_orders", id: :serial, force: :cascade do |t|
    t.integer "api_user_id"
    t.integer "user_id"
    t.string "order_reference"
    t.string "order_information", default: "{}"
    t.string "order_errors"
    t.integer "status"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "source"
    t.integer "test_instance_id"
    t.datetime "anonymized_at"
    t.index ["order_reference"], name: "index_api_orders_on_order_reference", order: "DESC NULLS LAST"
    t.index ["test_instance_id"], name: "api_orders_ti_index"
  end

  create_table "api_users", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.string "passphrase", null: false
    t.integer "status", default: 0, null: false
    t.string "authentication_token"
    t.datetime "authentication_token_expires_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "default_test_language"
    t.string "default_locale"
    t.integer "default_test_profile_id"
    t.boolean "force_test_mode", default: true
    t.string "access_control_allow_origin"
    t.string "access_control_allow_methods"
    t.datetime "deleted_at", precision: nil
    t.boolean "force_api_user_brand", default: false
    t.boolean "force_identity_check", default: false
    t.string "metadata_1", default: "{}"
    t.jsonb "metadata", default: {}, null: false
    t.integer "days_before_test_instance_cancellation"
    t.boolean "api_ti_creation_requires_rc", default: false
    t.bigint "identity_provider_id"
    t.string "type"
    t.string "default_client_name"
    t.virtual "default_sanitized_client_name", type: :text, as: "lower((default_client_name)::text)", stored: true
    t.integer "source"
    t.string "support_email"
    t.index ["identity_provider_id"], name: "index_api_users_on_identity_provider_id"
    t.check_constraint "passphrase IS NOT NULL", name: "api_users_passphrase_null"
  end

  create_table "assessment_questions", id: :serial, force: :cascade do |t|
    t.json "json_data", default: {}
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "assessment_types", id: :serial, force: :cascade do |t|
    t.json "json_data", default: {}
    t.json "json_calculation_rules", default: {}
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "assessment_time"
  end

  create_table "average_grades", id: :serial, force: :cascade do |t|
    t.string "label"
    t.integer "score"
    t.string "cecrl_score"
    t.integer "average_gradable_id"
    t.string "average_gradable_type"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "number_of_computed_elements"
    t.index ["average_gradable_id"], name: "average_grades_composite_index", where: "((average_gradable_type)::text = 'TestInstance'::text)"
  end

  create_table "background_job_logs", id: :serial, force: :cascade do |t|
    t.string "job_id"
    t.string "name"
    t.string "status"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "ended_at", precision: nil
  end

  create_table "brands", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "logo_file"
    t.boolean "is_default"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "group_name"
    t.string "client_name"
    t.string "certificate_logo_file_name"
    t.string "certificate_logo_content_type"
    t.bigint "certificate_logo_file_size"
    t.datetime "certificate_logo_updated_at", precision: nil
    t.boolean "show_toeic_score_on_certificate"
    t.string "header_banner_certificate_file_name"
    t.string "header_banner_certificate_content_type"
    t.bigint "header_banner_certificate_file_size"
    t.datetime "header_banner_certificate_updated_at", precision: nil
    t.string "custom_certificate"
    t.boolean "certificate_branded", default: false
    t.boolean "application_branded", default: false
    t.text "generated_css"
    t.text "additional_css"
    t.jsonb "custom_style_attributes", default: {}
    t.string "logo_file_name"
    t.integer "logo_file_size"
    t.string "logo_content_type"
    t.datetime "logo_updated_at", precision: nil
    t.boolean "hide_end_of_test_forms", default: false
  end

  create_table "certificates", id: :serial, force: :cascade do |t|
    t.integer "test_instance_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "evaluation_id"
    t.index ["evaluation_id"], name: "certificate_evaluation_index"
  end

  create_table "challenge_logs", id: :serial, force: :cascade do |t|
    t.integer "challenge_id"
    t.integer "question_id"
    t.boolean "success", default: false
    t.boolean "rated", default: false
    t.datetime "rated_at", precision: nil
    t.integer "production_user_id"
    t.float "production_user_main_rating"
    t.float "production_user_main_rating_deviation"
    t.float "production_user_main_volatility"
    t.float "production_user_production_rating"
    t.float "production_user_production_rating_deviation"
    t.float "production_user_production_volatility"
    t.integer "reception_user_id"
    t.float "reception_user_main_rating"
    t.float "reception_user_main_rating_deviation"
    t.float "reception_user_main_volatility"
    t.float "reception_user_reception_rating"
    t.float "reception_user_reception_rating_deviation"
    t.float "reception_user_reception_volatility"
    t.string "question_element_type"
    t.integer "question_element_id"
    t.float "question_element_production_rating"
    t.float "question_element_production_rating_deviation"
    t.float "question_element_production_volatility"
    t.float "question_element_reception_rating"
    t.float "question_element_reception_rating_deviation"
    t.float "question_element_reception_volatility"
    t.integer "scoring_batch_id"
    t.float "question_element_main_reception_rating"
    t.float "question_element_main_reception_rating_deviation"
    t.float "question_element_main_reception_volatility"
    t.float "question_element_main_production_rating"
    t.float "question_element_main_production_rating_deviation"
    t.float "question_element_main_production_volatility"
    t.float "question_element_average_rating"
    t.float "question_element_average_rating_deviation"
    t.float "question_element_average_volatility"
    t.float "production_user_average_rating"
    t.float "production_user_average_rating_deviation"
    t.float "production_user_average_volatility"
    t.float "reception_user_average_rating"
    t.float "reception_user_average_rating_deviation"
    t.float "reception_user_average_volatility"
    t.datetime "challenge_validated_at", precision: nil, null: false
    t.datetime "production_answered_at", precision: nil
    t.datetime "reception_answered_at", precision: nil
    t.integer "production_id"
    t.integer "reception_id"
    t.index ["production_id"], name: "index_challenge_logs_on_production_id"
    t.index ["question_element_id", "reception_user_id", "production_user_id"], name: "qe_per_user"
    t.index ["rated", "reception_user_id", "production_user_id"], name: "rated_per_user"
    t.index ["reception_id"], name: "index_challenge_logs_on_reception_id"
    t.index ["reception_user_id", "production_user_id"], name: "per_user"
    t.index ["scoring_batch_id"], name: "index_challenge_logs_on_scoring_batch_id"
  end

  create_table "challenges", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "production_layout_id"
    t.integer "reception_layout_id"
    t.integer "production_duration", default: 0
    t.integer "reception_duration", default: 0
    t.string "linguist_id"
    t.integer "status", default: 0
    t.datetime "deleted_at", precision: nil
    t.float "average_rating_production", default: 1500.0
    t.float "average_rating_reception", default: 1500.0
    t.string "list_skills", default: [], null: false, array: true
  end

  create_table "challenges_tags", id: :serial, force: :cascade do |t|
    t.integer "challenge_id"
    t.integer "tag_id"
  end

  create_table "client_comments", id: :serial, force: :cascade do |t|
    t.integer "client_config_id"
    t.string "languages"
    t.text "comment"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "client_configs", id: :serial, force: :cascade do |t|
    t.string "client_name"
    t.boolean "active", default: false
    t.boolean "expire_test_instances", default: false
    t.integer "expire_test_instances_limit", default: 0
    t.boolean "send_reminders", default: true
    t.integer "count_of_reminders_to_send", default: 0
    t.integer "days_between_reminders", default: 0
    t.boolean "urgent_mode", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "surcharged_certificate_email", default: ""
    t.text "cced_email"
    t.boolean "skip_oral", default: false
    t.boolean "has_attached_certificate", default: true
    t.boolean "block_direct_candidate_contact", default: false
    t.boolean "strong_identity_check", default: false
    t.boolean "deliver_certificate_to_client", default: true
    t.boolean "skip_check_caracters", default: false
    t.integer "systematic_peer_review", default: 0
    t.integer "systematic_peer_review_counter", default: 0
    t.string "custom_message"
    t.boolean "practice_pack_mention", default: true
    t.virtual "sanitized_client_name", type: :text, as: "lower((client_name)::text)", stored: true
    t.boolean "expose_dashboard_url_in_api", default: false
    t.boolean "expose_identity_photos_in_api", default: false
    t.boolean "disable_ai_assessments", default: false
    t.boolean "disable_ai_model_training", default: false, null: false
    t.boolean "force_secure_browser", default: false, null: false
    t.boolean "enable_wheebox_proctoring", default: false
    t.index ["client_name"], name: "index_client_configs_on_client_name"
    t.index ["sanitized_client_name"], name: "index_client_configs_on_sanitized_client_name"
  end

  create_table "currencies", id: :serial, force: :cascade do |t|
    t.string "code"
    t.float "eur_exchange_rate"
    t.datetime "eur_exchange_rate_updated_at", precision: nil
  end

  create_table "delayed_jobs", id: :serial, force: :cascade do |t|
    t.integer "priority", default: 0, null: false
    t.integer "attempts", default: 0, null: false
    t.text "handler", null: false
    t.text "last_error"
    t.datetime "run_at", precision: nil
    t.datetime "locked_at", precision: nil
    t.datetime "failed_at", precision: nil
    t.string "locked_by"
    t.string "queue"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "direct_users", id: :serial, force: :cascade do |t|
    t.string "static_authentication_token"
    t.datetime "static_authentication_token_expires_at", precision: nil
    t.integer "status", default: 0, null: false
    t.integer "default_test_profile_id"
    t.string "pipplet_clients_api_url"
    t.string "pipplet_clients_api_token"
    t.integer "pipplet_clients_campaign_id"
    t.integer "pipplet_clients_account_id"
    t.string "pipplet_clients_campaign_test_language"
    t.integer "pipplet_clients_campaign_test_profile_id"
    t.string "pipplet_clients_campaign_name"
    t.string "pipplet_clients_campaign_hashed_id"
    t.string "pipplet_clients_campaign_state"
    t.string "pipplet_clients_campaign_accuracy"
    t.string "pipplet_clients_account_name"
    t.string "pipplet_clients_account_api_group"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.boolean "pipplet_clients_account_test_mode", default: true
    t.datetime "last_pipplet_clients_sync_at", precision: nil
    t.string "pipplet_clients_client_email"
    t.string "pipplet_clients_client_first_name"
    t.string "pipplet_clients_client_last_name"
    t.boolean "fake_emails"
    t.boolean "skip_email", default: false
    t.integer "target_audience", default: 0, null: false
  end

  create_table "dynamic_question_data", id: :serial, force: :cascade do |t|
    t.string "dqd_able_type"
    t.integer "dqd_able_id"
    t.integer "question_element_id"
    t.integer "user_id"
    t.text "data", default: "{}"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "anonymized_at"
    t.index ["dqd_able_id"], name: "index_dynamic_question_data_on_dqd_able_id"
  end

  create_table "evaluation_delays", id: :serial, force: :cascade do |t|
    t.string "delay_label"
    t.integer "graded_time_limit"
    t.integer "soon_overdue_time_limit"
    t.float "payement_rate"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "evaluations", id: :serial, force: :cascade do |t|
    t.integer "test_instance_id"
    t.integer "examiner_id"
    t.integer "evaluation_delay_id"
    t.integer "reminder_nb", default: 0
    t.string "status"
    t.string "payment_type"
    t.string "evaluation_goal"
    t.datetime "assigned_at", precision: nil
    t.datetime "canceled_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.datetime "delivered_at", precision: nil
    t.datetime "assessed_at", precision: nil
    t.datetime "last_reminder_at", precision: nil
    t.datetime "soon_overdue_at", precision: nil
    t.datetime "validated_at", precision: nil
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.datetime "overdue_at", precision: nil
    t.boolean "urgent", default: false
    t.string "assessment_link"
    t.boolean "is_reviewed", default: false
    t.string "certification_type"
    t.datetime "start_assessment_at", precision: nil
    t.json "json_assessment", default: {}
    t.integer "assessment_type_id"
    t.string "pdf_certificate_file_name"
    t.string "pdf_certificate_content_type"
    t.bigint "pdf_certificate_file_size"
    t.datetime "pdf_certificate_updated_at", precision: nil
    t.boolean "checks_passed", default: false
    t.string "check_details"
    t.integer "invoice_request_id"
    t.string "payment_status"
    t.boolean "presented_for_review", default: false
    t.datetime "presented_for_review_at", precision: nil
    t.bigint "reviewed_evaluation_id"
    t.integer "ai_job_attempts", default: 0
    t.text "ai_written_response"
    t.text "ai_spoken_response"
    t.datetime "anonymized_at"
    t.integer "ai_written_score"
    t.integer "ai_spoken_score"
    t.boolean "ai_written_completed", default: false
    t.boolean "ai_spoken_completed", default: false
    t.integer "ai_spoken_attempts", default: 0
    t.integer "ai_written_attempts", default: 0
    t.datetime "deleted_at"
    t.index ["assessment_type_id"], name: "index_evaluations_on_assessment_type_id"
    t.index ["deleted_at"], name: "index_evaluations_on_deleted_at"
    t.index ["examiner_id"], name: "index_evaluations_on_examiner_id"
    t.index ["reviewed_evaluation_id"], name: "index_evaluations_on_reviewed_evaluation_id"
    t.index ["status"], name: "index_evaluations_on_status"
    t.index ["test_instance_id"], name: "index_evaluations_on_test_instance_id"
  end

  create_table "evaluations_tags", id: false, force: :cascade do |t|
    t.bigint "tag_id", null: false
    t.bigint "evaluation_id", null: false
  end

  create_table "examiner_fees", id: :serial, force: :cascade do |t|
    t.integer "assessment_type_id"
    t.integer "examiner_id"
    t.float "fee"
    t.string "currency"
    t.index ["examiner_id", "assessment_type_id"], name: "index_examiner_fees_on_examiner_id_and_assessment_type_id", unique: true
  end

  create_table "examiner_other_services", force: :cascade do |t|
    t.string "description"
    t.integer "service_type"
    t.date "service_date"
    t.float "amount"
    t.bigint "examiner_id"
    t.bigint "invoice_request_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["examiner_id"], name: "index_examiner_other_services_on_examiner_id"
    t.index ["invoice_request_id"], name: "index_examiner_other_services_on_invoice_request_id"
  end

  create_table "examiners", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "language"
    t.integer "status", default: 0
    t.integer "rank"
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "max_evaluations"
    t.integer "user_id"
    t.datetime "max_eval_alert_at", precision: nil
    t.boolean "beta_features", default: false
    t.integer "peer_review_interval", default: 0
    t.integer "peer_review_number", default: 1
    t.integer "number_of_eval_since_last_peer_review", default: 0
    t.datetime "next_unavailable_start_date", precision: nil
    t.datetime "next_unavailable_end_date", precision: nil
    t.string "default_currency", default: "EUR"
    t.boolean "is_requestable", default: false
    t.boolean "real", default: true
    t.integer "max_assessment_time"
    t.integer "max_assessment_time_limit", default: 240
    t.string "street", default: ""
    t.string "street_number", default: ""
    t.string "city", default: ""
    t.string "state", default: ""
    t.string "zip_code", default: ""
    t.string "country_code", default: ""
    t.string "registration_number", default: ""
    t.string "company_name", default: ""
    t.string "vat_number", default: ""
    t.boolean "has_vat", default: false
    t.string "bank_country"
    t.string "bank_city"
    t.string "bank_name"
    t.string "bank_iban"
    t.string "bank_bic"
    t.string "bank_accountnumber"
    t.string "bank_sortcode"
    t.string "bank_currency"
    t.boolean "is_ai", default: false
    t.bigint "wise_recipient_id"
    t.string "bank_branch_code"
    t.jsonb "ai_config", default: {}
    t.index ["status"], name: "examiners_real_index", where: "((deleted_at IS NULL) AND (\"real\" IS TRUE))"
  end

  create_table "feedbacks", id: :serial, force: :cascade do |t|
    t.string "content"
    t.string "question"
    t.integer "feedback_type"
    t.integer "user_id"
    t.string "user_mail"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "grades", id: :serial, force: :cascade do |t|
    t.integer "certificate_id"
    t.string "label"
    t.integer "score"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "cecrl_score", default: ""
    t.index ["certificate_id"], name: "grades_certificate_index"
  end

  create_table "identity_providers", force: :cascade do |t|
    t.string "name", null: false
    t.string "sp_entity_id", null: false
    t.integer "vendor", default: 0, null: false
    t.string "metadata_url"
    t.string "metadata_xml"
    t.jsonb "settings", default: {}, null: false
    t.datetime "settings_cached_until"
    t.jsonb "assertion_response_options", default: {}, null: false
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0
    t.boolean "create_ti_on_first_sign_in", default: false, null: false
    t.string "first_name_saml_attribute"
    t.string "last_name_saml_attribute"
    t.bigint "sso_user_id"
    t.string "order_reference_saml_attribute"
    t.index ["sso_user_id"], name: "index_identity_providers_on_sso_user_id"
  end

  create_table "images", id: :serial, force: :cascade do |t|
    t.string "name"
    t.text "caption"
    t.integer "position", default: 0
    t.integer "attachable_id"
    t.string "attachable_type"
    t.string "data_file_name"
    t.string "data_content_type"
    t.integer "data_file_size"
    t.datetime "data_updated_at", precision: nil
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "invoice_requests", id: :serial, force: :cascade do |t|
    t.string "status"
    t.integer "examiner_id"
    t.datetime "created_at", precision: nil
    t.datetime "paid_at", precision: nil
    t.string "invoice_currency", default: "EUR"
    t.string "bank_account_currency", default: "EUR"
    t.float "total_in_fee_currency"
    t.datetime "request_sent_at", precision: nil
    t.datetime "invoice_received_at", precision: nil
    t.json "json_examiner_fees", default: {}
    t.datetime "validated_at", precision: nil
    t.integer "simple_evals_counter"
    t.integer "detailed_evals_counter"
    t.string "month"
    t.string "invoice_pdf_file_name"
    t.string "invoice_pdf_content_type"
    t.bigint "invoice_pdf_file_size"
    t.datetime "invoice_pdf_updated_at", precision: nil
    t.float "requested_amount"
    t.datetime "validated_auto_at", precision: nil
    t.float "vat_amount", default: 0.0
    t.string "examiner_invoice_reference", default: ""
    t.float "requested_amount_evaluations"
    t.float "requested_amount_other_services"
    t.integer "written_evals_counter", default: 0
    t.integer "spoken_evals_counter", default: 0
  end

  create_table "languages", id: false, force: :cascade do |t|
    t.string "name"
    t.string "pretty_name"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "layout_zones", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "layout_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["layout_id"], name: "index_layout_zones_on_layout_id"
  end

  create_table "layouts", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "filename"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "next_questionables", id: :serial, force: :cascade do |t|
    t.integer "challenge_id"
    t.float "challenge_average_rating"
    t.integer "user_id"
    t.integer "production_id"
    t.integer "reception_id"
    t.integer "questionable_user_id"
    t.float "questionable_user_rating"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "test_instance_id"
    t.index ["challenge_id"], name: "index_next_questionables_on_challenge_id"
    t.index ["production_id"], name: "index_next_questionables_on_production_id"
    t.index ["test_instance_id"], name: "index_next_questionables_on_test_instance_id"
  end

  create_table "past_challenges", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "challenge_id"
    t.integer "challenger_user_id"
    t.integer "scoring_batch_id"
    t.boolean "rated"
    t.datetime "rated_at", precision: nil
    t.boolean "listening"
    t.boolean "reading"
    t.boolean "writing"
    t.boolean "speaking"
    t.integer "successes_count"
    t.integer "questions_count"
    t.integer "question_elements_count"
    t.boolean "is_production"
    t.boolean "is_reception"
    t.float "rating"
    t.float "rating_deviation"
    t.float "challenger_rating"
    t.float "challenger_rating_deviation"
    t.float "challenge_average_rating"
    t.float "challenge_distance_to_closest_boundary"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "answered_at", precision: nil
    t.datetime "production_answered_at", precision: nil
    t.datetime "reception_answered_at", precision: nil
    t.integer "production_id"
    t.integer "reception_id"
    t.index ["challenge_id"], name: "index_past_challenges_on_challenge_id"
    t.index ["is_production"], name: "index_past_challenges_on_is_production"
    t.index ["is_reception"], name: "index_past_challenges_on_is_reception"
    t.index ["user_id", "answered_at"], name: "index_past_challenges_on_user_id_and_answered_at"
    t.index ["user_id", "rated", "answered_at"], name: "index_past_challenges_on_user_id_and_rated_and_answered_at"
  end

  create_table "pghero_query_stats", force: :cascade do |t|
    t.text "database"
    t.text "user"
    t.text "query"
    t.bigint "query_hash"
    t.float "total_time"
    t.bigint "calls"
    t.datetime "captured_at", precision: nil
    t.index ["database", "captured_at"], name: "index_pghero_query_stats_on_database_and_captured_at"
  end

  create_table "production_metadata", id: :serial, force: :cascade do |t|
    t.integer "production_id"
    t.text "production_transcription"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "typing_speed"
    t.string "predicted_cecrl_score"
    t.index ["production_id"], name: "production_metadata_index"
  end

  create_table "productions", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.datetime "answered_at", precision: nil
    t.integer "challenge_id"
    t.integer "status", default: 0
    t.datetime "displayed_at", precision: nil
    t.datetime "deleted_at", precision: nil
    t.boolean "super", default: false
    t.integer "test_instance_id"
    t.index ["challenge_id"], name: "index_productions_on_challenge_id"
    t.index ["status"], name: "index_productions_on_status"
    t.index ["test_instance_id"], name: "index_productions_on_test_instance_id"
    t.index ["user_id"], name: "index_productions_on_user_id"
  end

  create_table "question_elements", id: :serial, force: :cascade do |t|
    t.integer "question_id"
    t.integer "order"
    t.boolean "scorable"
    t.integer "production_score_id"
    t.integer "reception_score_id"
    t.string "type"
    t.text "content", default: "{}"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "static_valid_answer"
    t.datetime "deleted_at", precision: nil
    t.integer "status", default: 0
    t.index ["question_id"], name: "index_question_elements_on_question_id"
  end

  create_table "questions", id: :serial, force: :cascade do |t|
    t.integer "layout_zone_id"
    t.integer "challenge_id"
    t.integer "order"
    t.string "type"
    t.integer "question_for"
    t.string "label"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.index ["challenge_id"], name: "index_questions_on_challenge_id"
    t.index ["layout_zone_id"], name: "index_questions_on_layout_zone_id"
  end

  create_table "questions_tags", id: :serial, force: :cascade do |t|
    t.integer "question_id"
    t.integer "tag_id"
  end

  create_table "receptions", id: :serial, force: :cascade do |t|
    t.integer "production_id"
    t.integer "user_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.datetime "answered_at", precision: nil
    t.integer "challenge_id"
    t.integer "status", default: 0
    t.datetime "displayed_at", precision: nil
    t.datetime "deleted_at", precision: nil
    t.integer "test_instance_id"
    t.index ["challenge_id"], name: "index_receptions_on_challenge_id"
    t.index ["production_id"], name: "receptions_production_index", where: "(deleted_at IS NULL)"
    t.index ["status", "test_instance_id"], name: "receptiond_production_index", where: "(deleted_at IS NULL)"
    t.index ["status"], name: "index_receptions_on_status"
    t.index ["user_id"], name: "index_receptions_on_user_id"
  end

  create_table "recordings", id: :serial, force: :cascade do |t|
    t.string "name"
    t.text "caption"
    t.integer "position", default: 0
    t.integer "attachable_id"
    t.string "attachable_type"
    t.string "data_file_name"
    t.string "data_content_type"
    t.integer "data_file_size"
    t.datetime "data_updated_at", precision: nil
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "registration_codes", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.string "code", null: false
    t.datetime "expiration_date", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "max_usages"
    t.integer "status", default: 0, null: false
  end

  create_table "remote_clients", id: :serial, force: :cascade do |t|
    t.string "rule_name"
    t.integer "api_user_id"
    t.string "client_type"
    t.string "client_name"
    t.string "test_language"
    t.integer "pipplet_clients_campaign_id"
    t.integer "status", default: 0
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "pipplet_clients_account_id"
  end

  create_table "results", id: :serial, force: :cascade do |t|
    t.integer "reception_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "status", default: 0
    t.datetime "deleted_at", precision: nil
    t.integer "test_instance_id"
  end

  create_table "roles", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "resource_id"
    t.string "resource_type"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["name", "resource_type", "resource_id"], name: "index_roles_on_name_and_resource_type_and_resource_id"
  end

  create_table "score_versions", id: :serial, force: :cascade do |t|
    t.integer "score_id"
    t.integer "scoring_batch_id"
    t.datetime "created_at", precision: nil
    t.float "rating"
    t.float "rating_deviation"
    t.float "volatility"
    t.string "scorable_type"
    t.integer "scorable_id"
  end

  create_table "scores", id: :serial, force: :cascade do |t|
    t.float "rating"
    t.float "rating_deviation"
    t.float "volatility"
    t.string "scorable_type"
    t.integer "scorable_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "label"
    t.integer "scoring_batch_id"
  end

  create_table "scoring_batches", id: :serial, force: :cascade do |t|
    t.integer "trigger"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "statistics", force: :cascade do |t|
    t.string "label"
    t.float "data", default: 0.0
    t.datetime "data_time", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tags", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "value"
  end

  create_table "tags_test_instances", id: false, force: :cascade do |t|
    t.bigint "tag_id", null: false
    t.bigint "test_instance_id", null: false
  end

  create_table "technical_issues", id: :serial, force: :cascade do |t|
    t.string "status"
    t.integer "test_instance_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["test_instance_id"], name: "index_technical_issues_on_test_instance_id"
  end

  create_table "test_instance_validation_details", force: :cascade do |t|
    t.bigint "test_instance_id"
    t.string "aasm_state"
    t.json "validation_details", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["test_instance_id"], name: "index_test_instance_validation_details_on_test_instance_id"
  end

  create_table "test_instance_validations", force: :cascade do |t|
    t.string "name", default: ""
    t.json "final_validation_rules", default: {}
    t.json "validation_launching_rules", default: {}
    t.json "conditional_validation_rules", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "tekis_rules", default: {}
    t.json "redos_rules", default: {}
  end

  create_table "test_instances", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "test_profile_id"
    t.integer "direct_user_id"
    t.integer "api_user_id"
    t.string "status", default: "initialized", null: false
    t.datetime "begin_date", precision: nil
    t.datetime "end_date", precision: nil
    t.string "test_language"
    t.integer "result_rating"
    t.datetime "graded_at", precision: nil
    t.string "report_file_name"
    t.string "report_content_type"
    t.integer "report_file_size"
    t.datetime "report_updated_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.integer "pipplet_clients_campaign_id"
    t.string "uuid", null: false
    t.boolean "skip_audio_test", default: false
    t.boolean "skip_tutorial", default: false
    t.boolean "send_reminders", default: true
    t.integer "count_of_reminders_sent", default: 0
    t.datetime "last_reminder_sent_at", precision: nil
    t.integer "question_set_id"
    t.integer "examiner_id"
    t.boolean "test_mode", default: true
    t.boolean "skip_identity_check", default: true
    t.boolean "skip_browser_check", default: false
    t.integer "cached_count_of_questions_answered"
    t.integer "cached_count_of_questions_to_answer"
    t.string "client_type"
    t.integer "client_id"
    t.string "client_name"
    t.string "client_contact_email"
    t.string "client_contact_first_name"
    t.string "client_contact_last_name"
    t.integer "evaluation_id"
    t.string "redirection_url"
    t.boolean "skip_email", default: false
    t.string "duplicate"
    t.boolean "review_required", default: false
    t.datetime "sent_for_evaluation_date", precision: nil
    t.string "client_contact_locale"
    t.datetime "review_required_modified_at", precision: nil
    t.integer "delivered_evaluation_id"
    t.boolean "monitored", default: false
    t.string "source_type"
    t.integer "cut_min_limit"
    t.integer "cut_max_limit"
    t.boolean "had_check_failed", default: false
    t.boolean "certificate_sent_to_candidate"
    t.float "time_multiplier", default: 1.0
    t.string "skills_tested", default: [], array: true
    t.integer "count_of_tekis_reminders", default: 0
    t.datetime "last_tekis_email_send_at", precision: nil
    t.integer "count_of_sms_reminders", default: 0
    t.datetime "last_sms_reminder_sent_at", precision: nil
    t.boolean "sms_reminders", default: false
    t.datetime "regrade_requested_at"
    t.virtual "sanitized_client_name", type: :text, as: "lower((client_name)::text)", stored: true
    t.datetime "anonymized_at"
    t.integer "wheebox_attempt_id"
    t.boolean "wheebox_face_training_completed"
    t.index ["api_user_id"], name: "index_test_instances_on_api_user_id"
    t.index ["client_name"], name: "index_test_instances_on_client_name"
    t.index ["graded_at"], name: "test_instances_composite_index_deleted_graded", where: "(deleted_at IS NULL)"
    t.index ["question_set_id"], name: "index_test_instances_on_question_set_id"
    t.index ["sanitized_client_name"], name: "index_test_instances_on_sanitized_client_name"
    t.index ["status"], name: "index_test_instances_on_status"
    t.index ["test_language"], name: "index_test_instances_on_test_language"
    t.index ["test_mode"], name: "index_test_instances_on_test_mode"
    t.index ["test_profile_id"], name: "index_test_instances_on_test_profile_id"
    t.index ["user_id"], name: "index_test_instances_on_user_id"
    t.index ["uuid"], name: "index_test_instances_on_uuid"
  end

  create_table "test_profiles", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.integer "status", default: 0, null: false
    t.integer "next_questionable_profile"
    t.integer "max_questions_to_answer"
    t.string "linguist_ids"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.boolean "force_multilingual", default: false
    t.integer "certification_type", default: 0
    t.integer "test_taker_type", default: 0
    t.boolean "show_skip_button", default: false
    t.string "fallback_set"
    t.text "examiner_description"
    t.text "internal_description"
    t.string "additional_linguist_ids"
    t.bigint "test_instance_validation_id"
    t.integer "client_type"
    t.integer "level"
    t.integer "test_type", default: 2
    t.string "translated_languages"
    t.string "additional_translated_languages"
    t.string "test_languages", default: [], array: true
    t.boolean "api_exposed", default: false
    t.virtual "slug", type: :string, as: "regexp_replace(regexp_replace(lower((name)::text), '[^a-z0-9\\-_]+'::text, '-'::text, 'gi'::text), '(^-+|-+$)'::text, ''::text, 'g'::text)", stored: true
    t.boolean "talent_ai", default: false
  end

  create_table "test_scores", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "score"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_test_scores_on_user_id"
  end

  create_table "user_groups", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "examiner_en_id"
    t.integer "examiner_de_id"
    t.integer "examiner_es_id"
    t.integer "examiner_fr_id"
    t.integer "examiner_it_id"
    t.integer "examiner_nl_id"
    t.integer "examiner_ar_id"
    t.integer "examiner_ru_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "examiner_pt_id"
    t.integer "examiner_ptbr_id"
    t.integer "examiner_el_id"
    t.integer "examiner_sv_id"
    t.integer "examiner_zhcn_id"
    t.integer "examiner_ja_id"
    t.integer "examiner_ko_id"
    t.integer "examiner_th_id"
    t.integer "examiner_vi_id"
    t.integer "examiner_tr_id"
    t.integer "examiner_he_id"
    t.integer "examiner_pl_id"
    t.integer "examiner_ms_id"
    t.integer "examiner_hi_id"
    t.integer "examiner_cs_id"
    t.integer "examiner_da_id"
    t.integer "examiner_lb_id"
    t.integer "examiner_no_id"
    t.integer "examiner_eu_id"
    t.integer "examiner_uk_id"
    t.integer "examiner_hu_id"
    t.integer "examiner_fi_id"
    t.integer "examiner_hr_id"
    t.integer "examiner_ro_id"
    t.integer "examiner_sl_id"
    t.integer "examiner_sr_id"
    t.integer "examiner_bg_id"
    t.integer "examiner_sk_id"
    t.integer "examiner_et_id"
    t.integer "examiner_lt_id"
    t.integer "examiner_lv_id"
    t.integer "examiner_zhyue_id"
    t.integer "examiner_sq_id"
  end

  create_table "user_languages", id: :serial, force: :cascade do |t|
    t.boolean "mothertongue", default: false
    t.string "level", default: "nd"
    t.string "name"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_user_languages_on_user_id"
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.datetime "deleted_at", precision: nil
    t.string "language_name"
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at", precision: nil
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "status"
    t.boolean "understands_cgu"
    t.string "first_name"
    t.string "last_name"
    t.string "group"
    t.string "uuid", null: false
    t.integer "registration_code_id"
    t.datetime "disabled", precision: nil
    t.string "authentication_token"
    t.text "testdata"
    t.string "phone_number"
    t.integer "current_ti"
    t.boolean "is_vip", default: false
    t.string "identity_status"
    t.string "strip_identity_image_id"
    t.datetime "verified_at", precision: nil
    t.string "stripe_verification_session_id"
    t.boolean "accepts_marketing_contacts", default: false
    t.datetime "ai_consent_granted_at"
    t.datetime "anonymized_at"
    t.bigint "identity_provider_id"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["identity_provider_id"], name: "index_users_on_identity_provider_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  create_table "users_roles", id: false, force: :cascade do |t|
    t.integer "user_id"
    t.integer "role_id"
    t.index ["user_id", "role_id"], name: "index_users_roles_on_user_id_and_role_id"
  end

  add_foreign_key "examiner_fees", "assessment_types"
  add_foreign_key "examiner_other_services", "examiners"
  add_foreign_key "examiner_other_services", "invoice_requests"
  add_foreign_key "test_profiles", "test_instance_validations"
end
