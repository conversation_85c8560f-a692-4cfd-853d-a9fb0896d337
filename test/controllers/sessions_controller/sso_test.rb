require 'test_helper'

class SessionsController::SsoTest < ActionDispatch::IntegrationTest
  include SsoTestHelper
  include AlertsHelper
  include ActiveJob::TestHelper

  def setup
    @identity_provider = FactoryBot.create(:identity_provider)
    @sso_user = FactoryBot.create(:sso_user)
  end

  test '#saml_callback redirects to root_url if no IdentityProvider is found' do
    post saml_callback_url, params: { sp_entity_id: SecureRandom.hex }

    assert_equal I18n.t('devise.sessions.saml_callback.identity_provider.not_found'), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback redirects to root_url if IdentityProvider is not active' do
    identity_provider = FactoryBot.create(:identity_provider, status: :expired)

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_equal I18n.t('devise.sessions.saml_callback.identity_provider.not_found'), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback redirects to root_url if User is not found by name_id and has no email attribute (after_sign_in_path_for)' do
    stubs_saml_response(name_id: Faker::Internet.email(domain: 'pipplet.com'), attributes: { email: nil })

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }

    assert_equal I18n.t('devise.sessions.saml_callback.user.not_found', support_email: nil), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback redirects to root_url if User is not found by name_id and by email attribute (after_sign_in_path_for)' do
    stubs_saml_response(name_id: Faker::Internet.email(domain: 'pipplet.com'), attributes: { email: Faker::Internet.email(domain: 'pipplet.com') })

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }

    assert_equal I18n.t('devise.sessions.saml_callback.user.not_found', support_email: nil), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback logs out the user and redirects to root_url if no TI is found for a user (after_sign_in_path_for)' do
    user_without_ti = FactoryBot.create(:user, :confirmed, identity_provider: @identity_provider)

    stubs_saml_response(name_id: user_without_ti.email)

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }

    assert_equal I18n.t('test.none'), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback logs out the user and redirects to root_url if the user is an admin (after_sign_in_path_for)' do
    user_admin = FactoryBot.create(:user, :admin, identity_provider: @identity_provider)

    stubs_saml_response(name_id: user_admin.email)

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }

    assert_equal I18n.t('devise.sessions.saml_callback.user.not_found', support_email: nil), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback logs out the user and redirects to root_url if the user is an examiner (after_sign_in_path_for)' do
    user_examiner = FactoryBot.create(:user, :examiner, identity_provider: @identity_provider)
    FactoryBot.create(:examiner, user: user_examiner)

    stubs_saml_response(name_id: user_examiner.email)

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }

    assert_equal I18n.t('devise.sessions.saml_callback.user.not_found', support_email: nil), flash[:alert]
    assert_redirected_to root_url
  end

  test '#saml_callback logs in the user and redirects to the welcome_url if a TI is found for a user with an email in name_id' do
    test_instance = FactoryBot.create(:test_instance)
    user_with_ti = FactoryBot.create(:user, :confirmed, identity_provider: @identity_provider, test_instances: [test_instance])

    stubs_saml_response(name_id: user_with_ti.email)

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
    assert_equal user_with_ti, @controller.current_user
  end

  test '#saml_callback logs in the user and redirects to the welcome_url if a TI is found for a user with an email (with uppercase) in name_id' do
    test_instance = FactoryBot.create(:test_instance)
    user_with_ti = FactoryBot.create(:user, :confirmed, identity_provider: @identity_provider, test_instances: [test_instance])

    stubs_saml_response(name_id: user_with_ti.email.upcase)

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
    assert_equal user_with_ti, @controller.current_user
  end

  test '#saml_callback logs in the user and redirects to the welcome_url if a TI is found for a user with an email (with uppercase) in attributes' do
    test_instance = FactoryBot.create(:test_instance)
    user_with_ti = FactoryBot.create(:user, :confirmed, identity_provider: @identity_provider, test_instances: [test_instance])

    stubs_saml_response(attributes: { email: user_with_ti.email.upcase })

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
    assert_equal user_with_ti, @controller.current_user
  end

  test '#saml_callback logs in the user and redirects to the welcome_url if a TI is found for a user with no email in name_id but an email in attributes' do
    test_instance = FactoryBot.create(:test_instance)
    user_with_ti = FactoryBot.create(:user, :confirmed, identity_provider: @identity_provider, test_instances: [test_instance])

    stubs_saml_response(attributes: { email: user_with_ti.email })

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
    assert_equal user_with_ti, @controller.current_user
  end

  test '#saml_callback logs in the user and redirects to the welcome_url if the saml response is not valid' do
    test_instance = FactoryBot.create(:test_instance)
    user_with_ti = FactoryBot.create(:user, :confirmed, identity_provider: @identity_provider, test_instances: [test_instance])

    stubs_saml_response(name_id: user_with_ti.email, valid: false)

    post saml_callback_url, params: { sp_entity_id: @identity_provider.sp_entity_id }
    assert_equal I18n.t('devise.sessions.saml_callback.invalid', support_email: nil), flash[:alert]
    assert_redirected_to root_url
  end

  test '#create_user_from_response missing pre-requisites' do
    assert_no_difference %w[User.count TestInstance.count ApiOrder.count] do
      post saml_callback_url, params: { sp_entity_id: SecureRandom.hex }
    end

    stubs_saml_response(name_id: Faker::Internet.email(domain: 'pipplet.com'))

    assert_no_difference %w[User.count TestInstance.count ApiOrder.count] do
      post saml_callback_url, params: { sp_entity_id: FactoryBot.create(:identity_provider, :with_ti_creation, create_ti_on_first_sign_in: false).sp_entity_id }
    end
  end

  test '#create_user_from_response creates a TI if the IdentityProvider has create_ti_on_first_sign_in? and a SsoUser' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id
    }

    stubs_saml_response(name_id: email, attributes:)

    assert_difference %w[User.count TestInstance.count ApiOrder.count], 1 do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

      created_user = User.last
      assert_equal first_name, created_user.first_name
      assert_equal last_name, created_user.last_name
      assert_equal email, created_user.email
      assert_equal identity_provider, created_user.identity_provider

      created_test_instance = TestInstance.last
      assert_equal created_user, created_test_instance.user
      assert_equal @sso_user.default_test_profile_id, created_test_instance.test_profile_id
      assert_equal @sso_user.default_test_language, created_test_instance.test_language
      assert_equal @sso_user.force_test_mode, created_test_instance.test_mode
      assert created_test_instance.skip_email
      assert_equal @sso_user.id, created_test_instance.api_user_id
      assert_equal SOURCE_CLIENT_TYPES[:idp], created_test_instance.client_type

      api_order = ApiOrder.last
      assert_equal created_test_instance, api_order.test_instance
      assert_equal @sso_user, api_order.api_user
      assert_equal user_id, api_order.order_reference
      assert_equal attributes, api_order.order_information.to_h
      assert api_order.received?
      assert_equal @sso_user.source, ApiOrder.sources[api_order.source]
    end
  end

  test '#create_user_from_response creates a TI if the email has upper-case characters (name_id)' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = '<EMAIL>'
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id
    }

    stubs_saml_response(name_id: email, attributes:)

    assert_difference %w[User.count TestInstance.count ApiOrder.count], 1 do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

      created_user = User.last
      assert_equal email.downcase, created_user.email
    end
  end

  test '#create_user_from_response creates a TI if the email has upper-case characters (email attribute)' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = '<EMAIL>'
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id,
      'email' => email
    }

    stubs_saml_response(name_id: nil, attributes:)

    assert_difference %w[User.count TestInstance.count ApiOrder.count], 1 do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

      created_user = User.last
      assert_equal email.downcase, created_user.email
    end
  end

  test '#create_user_from_response with missing name_id and attributes' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    stubs_saml_response(name_id: nil, attributes: {})

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_redirected_to :root
    assert_equal I18n.t('devise.sessions.saml_callback.test_instance.invalid', support_email: @sso_user.support_email), flash[:alert]
    assert_alert(title: "SSO Error, IdentityProvider 'id'=#{identity_provider.id} 'name'=#{identity_provider.name}",
                 category: :sso,
                 meta: { first_name: nil,
                         last_name: nil,
                         email: nil,
                         response: {} },
                 message: 'Error while creating a first TestInstance, error: [TestInstance.create_with_user] No user email')
  end

  test '#create_user_from_response with invalid TestProfile' do
    #    test_profile = FactoryBot.create(:test_profile, status: :expired)
    sso_user = FactoryBot.create(:sso_user, default_test_profile_id: rand(1000..2000))
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user:)
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id
    }

    stubs_saml_response(name_id: email, attributes:)

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_redirected_to :root
    assert_equal I18n.t('devise.sessions.saml_callback.test_instance.invalid', support_email: sso_user.support_email), flash[:alert]
    assert_alert(title: "SSO Error, IdentityProvider 'id'=#{identity_provider.id} 'name'=#{identity_provider.name}",
                 category: :sso,
                 meta: { first_name:,
                         last_name:,
                         email:,
                         response: {} },
                 message: 'Error while creating a first TestInstance, error: Validation failed: Test profile can\'t be blank')
  end

  test '#create_user_from_response with missing name_id but email in attributes' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id,
      'email' => email
    }
    stubs_saml_response(name_id: nil, attributes:)

    assert_difference %w[User.count TestInstance.count ApiOrder.count], 1 do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }
    end
  end

  test '#create_user_from_response with invalid name_id but email in attributes' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id,
      'email' => email
    }
    stubs_saml_response(name_id: 'this|is|not@valid', attributes:)

    assert_difference %w[User.count TestInstance.count ApiOrder.count], 1 do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }
    end
  end

  test '#create_user_from_response with missing attributes' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = Faker::Internet.email(domain: 'pipplet.com')
    stubs_saml_response(name_id: email, attributes: {})

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_redirected_to :root
    assert_equal I18n.t('devise.sessions.saml_callback.test_instance.invalid', support_email: @sso_user.support_email), flash[:alert]
    assert_alert(title: "SSO Error, IdentityProvider 'id'=#{identity_provider.id} 'name'=#{identity_provider.name}",
                 category: :sso,
                 meta: { first_name: nil,
                         last_name: nil,
                         email:,
                         response: {} },
                 message: 'Error while creating a first TestInstance, error: Validation failed: First name can\'t be blank, Last name can\'t be blank')
  end

  test '#create_user_from_response redirects to root_url and adds an Alert if an error occurs while creating a TI' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    error_message = Faker::Company.buzzword

    stubs_saml_response(name_id: email, attributes: {
                          'givenName' => first_name,
                          'familyName' => last_name
                        })

    SsoUser.any_instance.stubs(:create_test_instance).raises(StandardError.new(error_message))

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_redirected_to :root
    assert_equal I18n.t('devise.sessions.saml_callback.test_instance.invalid', support_email: @sso_user.support_email), flash[:alert]
    assert_alert(title: "SSO Error, IdentityProvider 'id'=#{identity_provider.id} 'name'=#{identity_provider.name}",
                 category: :sso,
                 meta: { first_name:,
                         last_name:,
                         email:,
                         response: {} },
                 message: "Error while creating a first TestInstance, error: #{error_message}")
  end
  test '#create_user_from_response does not create a second TI if a TI with the user email already exists for the same SsoUser' do
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name

    FactoryBot.create(:test_instance, api_user_id: @sso_user.id, user: FactoryBot.create(:user, email:))
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)

    stubs_saml_response(name_id: email, attributes: {
                          'givenName' => first_name,
                          'familyName' => last_name
                        })

    assert_no_difference %w[User.count TestInstance.count ApiOrder.count] do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }
    end
  end

  test '#create_user_from_response creates a second TI if a TI with the user email already exists, but not for this SsoUser' do
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name

    FactoryBot.create(:test_instance, user: FactoryBot.create(:user, email:))
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)

    stubs_saml_response(name_id: email, attributes: {
                          'givenName' => first_name,
                          'familyName' => last_name
                        })

    assert_difference %w[TestInstance.count ApiOrder.count], 1 do
      assert_no_difference 'User.count' do
        post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }
      end
    end
  end

  test '#create_user_from_response triggers the User creation on Pclients if a RemoteClient is linked to the SsoUser' do
    identity_provider = FactoryBot.create(:identity_provider, :with_ti_creation, sso_user: @sso_user)
    email = Faker::Internet.email(domain: 'pipplet.com')
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    user_id = SecureRandom.hex
    attributes = {
      'givenName' => first_name,
      'familyName' => last_name,
      'userId' => user_id
    }

    stubs_saml_response(name_id: email, attributes:)

    FactoryBot.create(:remote_client, api_user: @sso_user)

    stub_request(:post, "#{PippletClientsApi.api_url}/api/v1/users").to_return(status: 200, body: {}.to_json)
    perform_enqueued_jobs do
      post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }
    end
    assert_requested(:post, "#{PippletClientsApi.api_url}/api/v1/users")
  end
end
