require 'test_helper'

# Created from a sample request/response on ETS IDAAS (testing)
class SessionsController::SsoResponseFromEtsTest < ActionDispatch::IntegrationTest
  include SsoTestHelper
  include Al<PERSON>sHelper

  def setup
    @valid_saml_response = Rails.root.join('test/fixtures/saml/ets_idaas/valid_saml_response.xml').read

    # unencrypted - dont validate signature
    OneLogin::RubySaml::Response.any_instance.stubs(:validate_signature).returns(true)

    # Fixed values from SAML Response, @name_id, min_clock_drift_time can be changed in the XML
    @sp_entity_id = 'a06d6bdd49528d0d088b726cb68e064f'
    @name_id = '<EMAIL>'
    min_clock_drift_time = DateTime.parse('2025-04-17T14:31:19.915Z')
    @valid_time = min_clock_drift_time
    @invalid_time = min_clock_drift_time + 6.seconds

    # SAML Response expects a preprod URL
    IdentityProvider.any_instance.stubs(:assertion_consumer_service_url).returns("https://preprod.pipplet.dev/public/saml/callback?sp_entity_id=#{@sp_entity_id}")

    @identity_provider = FactoryBot.create(:identity_provider,
                                           sp_entity_id: @sp_entity_id,
                                           metadata_xml: Rails.root.join('test/fixtures/saml/ets_idaas/identity_provider_metadata.xml').read)
    test_instance = FactoryBot.create(:test_instance)
    @user_with_ti = FactoryBot.create(:user, :confirmed, email: @name_id, identity_provider: @identity_provider, test_instances: [test_instance])
  end

  test '#saml_callback with a valid sample ETS response' do
    travel_to @valid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: @valid_saml_response
      }
      assert_equal @user_with_ti, @controller.current_user
    end
  end

  test '#saml_callback with a valid sample ETS response (invalid Time Drift)' do
    travel_to @invalid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: @valid_saml_response
      }
      saml_response = OneLogin::RubySaml::Response.new(@valid_saml_response, settings: @identity_provider.saml_settings)

      assert_nil @controller.current_user
      assert_alert(title: "SSO Error, IdentityProvider 'id'=#{@identity_provider.id} 'name'=#{@identity_provider.name}",
                   category: :sso,
                   meta: {
                     response: saml_response.response,
                     settings: @identity_provider.settings
                   },
                   message: 'Current time is on or after NotOnOrAfter condition (2025-04-17 14:31:25 UTC >= 2025-04-17 14:31:19 UTC + 5s), A valid SubjectConfirmation was not found on this Response')
    end
  end

  test '#saml_callback with an invalid URL in sample ETS response' do
    travel_to @valid_time do
      response = Rails.root.join('test/fixtures/saml/ets_idaas/wrong_url_saml_response.xml').read
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: response
      }

      saml_response = OneLogin::RubySaml::Response.new(response, settings: @identity_provider.saml_settings)

      assert_nil @controller.current_user
      assert_alert(title: "SSO Error, IdentityProvider 'id'=#{@identity_provider.id} 'name'=#{@identity_provider.name}",
                   category: :sso,
                   meta: {
                     response: saml_response.response,
                     settings: @identity_provider.settings
                   },
                   message: 'The response was received at https://invalid-url.dev/public/saml/callback?sp_entity_id=a06d6bdd49528d0d088b726cb68e064f instead of https://preprod.pipplet.dev/public/saml/callback?sp_entity_id=a06d6bdd49528d0d088b726cb68e064f, A valid SubjectConfirmation was not found on this Response')
    end
  end

  test '#saml_callback with an invalid URL in sample ETS response (assuming OneLogin provider) does not error on SubjectConfirmation' do
    @identity_provider.update!(vendor: :one_login)
    response = Rails.root.join('test/fixtures/saml/ets_idaas/wrong_url_saml_response.xml').read

    travel_to @valid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: response
      }

      saml_response = OneLogin::RubySaml::Response.new(response, settings: @identity_provider.saml_settings)

      assert_nil @controller.current_user
      assert_alert(title: "SSO Error, IdentityProvider 'id'=#{@identity_provider.id} 'name'=#{@identity_provider.name}",
                   category: :sso,
                   meta: {
                     response: saml_response.response,
                     settings: @identity_provider.settings
                   },
                   message: 'The response was received at https://invalid-url.dev/public/saml/callback?sp_entity_id=a06d6bdd49528d0d088b726cb68e064f instead of https://preprod.pipplet.dev/public/saml/callback?sp_entity_id=a06d6bdd49528d0d088b726cb68e064f')
    end
  end

  test '#saml_callback with invalid name_id and email in sample ETS response' do
    response = Rails.root.join('test/fixtures/saml/ets_idaas/invalid_name_id_invalid_email_saml_response.xml').read

    travel_to @valid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: response
      }

      assert_equal I18n.t('devise.sessions.saml_callback.user.not_found', support_email: nil), flash[:alert]
      assert_redirected_to root_url
    end
  end

  test '#saml_callback with invalid name_id but valid email in sample ETS response' do
    response = Rails.root.join('test/fixtures/saml/ets_idaas/invalid_name_id_valid_email_saml_response.xml').read

    travel_to @valid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: response
      }

      assert_equal @user_with_ti, @controller.current_user
    end
  end
end
