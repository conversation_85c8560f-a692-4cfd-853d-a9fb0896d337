require 'test_helper'

class SessionsController::AuthenticationMethodsTest < ActionDispatch::IntegrationTest
  include SsoTestHelper

  test 'a User connected via SSO first can sign in later with the confirmation_url' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    stubs_saml_response(name_id: user.email)

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }
    assert_equal user, @controller.current_user

    delete destroy_user_session_path

    assert_nil @controller.current_user

    get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

    assert_equal user, @controller.current_user
    assert_nil flash[:alert]
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
  end

  test 'a User connected via SSO first can access tests via the confirmation_url' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    stubs_saml_response(name_id: user.email)

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_equal user, @controller.current_user

    get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

    assert_nil flash[:alert]
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
  end

  test 'a User connected via confirmation_url first can sign in later via SSO' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

    assert_equal user, @controller.current_user

    delete destroy_user_session_path

    assert_nil @controller.current_user

    stubs_saml_response(name_id: user.email)
    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_equal user, @controller.current_user
    assert_nil flash[:alert]
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
  end

  test 'a User connected via confirmation_url first can access tests via SSO' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

    assert_equal user, @controller.current_user

    stubs_saml_response(name_id: user.email)
    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_nil flash[:alert]
    assert_redirected_to welcome_url(uuid: test_instance.uuid)
  end

  test 'a User connected via confirmation_url does not stay connected if SSO login fails' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

    assert_equal user, @controller.current_user

    stubs_saml_response(name_id: user.email, valid: false)
    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_nil @controller.current_user
    assert_equal I18n.t('devise.sessions.saml_callback.invalid', support_email: nil), flash[:alert]
    assert_redirected_to root_url
  end

  test 'a User connected via SSO first cannot access tests via an invalid confirmation_url' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    stubs_saml_response(name_id: user.email)

    post saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id }

    assert_equal user, @controller.current_user

    get user_confirmation_url, params: { confirmation_token: SecureRandom.hex }

    assert_nil @controller.current_user
    assert_equal I18n.t('devise.confirmations.failure.invalid'), flash[:alert]
    assert_redirected_to root_url
  end

  test 'User connects through SSO, admin connects via token, User should not be disconnected' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    stubs_saml_response(name_id: user.email)

    open_session do |user_session|
      user_session.post(saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id })

      open_session do |admin_session|
        admin_session.get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

        admin_session.assert_redirected_to welcome_url(uuid: test_instance.uuid)
      end

      user_session.get welcome_url(uuid: test_instance.uuid)

      user_session.assert_response :ok
    end
  end

  test 'Admin connects via token, User connects through SSO, Admin should not be disconnected' do
    identity_provider = FactoryBot.create(:identity_provider)
    test_instance = FactoryBot.create(:test_instance)
    user = FactoryBot.create(:user, identity_provider:, test_instances: [test_instance])

    stubs_saml_response(name_id: user.email)

    open_session do |admin_session|
      admin_session.get user_confirmation_url, params: { confirmation_token: user.confirmation_token }

      admin_session.assert_redirected_to welcome_url(uuid: test_instance.uuid)

      open_session do |user_session|
        user_session.post(saml_callback_url, params: { sp_entity_id: identity_provider.sp_entity_id })
      end

      admin_session.get welcome_url(uuid: test_instance.uuid)

      admin_session.assert_response :ok
    end
  end
end
