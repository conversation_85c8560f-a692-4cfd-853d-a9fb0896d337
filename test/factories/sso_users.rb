# == Schema Information
#
# Table name: api_users
#
#  id                                     :integer          not null, primary key
#  access_control_allow_methods           :string
#  access_control_allow_origin            :string
#  api_ti_creation_requires_rc            :boolean          default(FALSE)
#  authentication_token                   :string
#  authentication_token_expires_at        :datetime
#  days_before_test_instance_cancellation :integer
#  default_client_name                    :string
#  default_locale                         :string
#  default_sanitized_client_name          :text
#  default_test_language                  :string
#  deleted_at                             :datetime
#  force_api_user_brand                   :boolean          default(FALSE)
#  force_identity_check                   :boolean          default(FALSE)
#  force_test_mode                        :boolean          default(TRUE)
#  metadata                               :jsonb            not null
#  metadata_1                             :string           default({})
#  name                                   :string           not null
#  passphrase                             :string           not null
#  source                                 :integer
#  status                                 :integer          default("initialized"), not null
#  support_email                          :string
#  type                                   :string
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  default_test_profile_id                :integer
#  identity_provider_id                   :bigint
#
# Indexes
#
#  index_api_users_on_identity_provider_id  (identity_provider_id)
#
FactoryBot.define do
  factory :sso_user do
    association :default_client_config, factory: :client_config

    name                    { "#{Faker::Name.first_name} #{Faker::Number.number}" }
    default_test_language   { :en }
    default_locale          { :en }
    force_test_mode         { false }
    default_test_profile_id { FactoryBot.create(:test_profile).id }
    source                  { ApiOrder::SSO_SOURCES.values.sample }
    support_email           { Faker::Internet.email }
  end
end
