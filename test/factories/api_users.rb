# == Schema Information
#
# Table name: api_users
#
#  id                                     :integer          not null, primary key
#  access_control_allow_methods           :string
#  access_control_allow_origin            :string
#  api_ti_creation_requires_rc            :boolean          default(FALSE)
#  authentication_token                   :string
#  authentication_token_expires_at        :datetime
#  days_before_test_instance_cancellation :integer
#  default_client_name                    :string
#  default_locale                         :string
#  default_sanitized_client_name          :text
#  default_test_language                  :string
#  deleted_at                             :datetime
#  force_api_user_brand                   :boolean          default(FALSE)
#  force_identity_check                   :boolean          default(FALSE)
#  force_test_mode                        :boolean          default(TRUE)
#  metadata                               :jsonb            not null
#  metadata_1                             :string           default({})
#  name                                   :string           not null
#  passphrase                             :string           not null
#  source                                 :integer
#  status                                 :integer          default("initialized"), not null
#  support_email                          :string
#  type                                   :string
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  default_test_profile_id                :integer
#  identity_provider_id                   :bigint
#
# Indexes
#
#  index_api_users_on_identity_provider_id  (identity_provider_id)
#
FactoryBot.define do
  factory :api_user do
    name                                   { "#{Faker::Name.first_name} #{Faker::Number.number}" }
    status                                 { 1 } # Active by default
    authentication_token                   { Base64.encode64(name).chomp }
    authentication_token_expires_at        { 1.day.from_now }
    passphrase                             { SecureRandom.urlsafe_base64(32) }
    default_test_language                  { 'en' }
    default_locale                         { 'en' }
    access_control_allow_origin            { '' }
    access_control_allow_methods           { '' }
    force_api_user_brand                   { false }
    force_identity_check                   { false }
    force_test_mode                        { false }
    default_test_profile_id                { FactoryBot.create(:test_profile).id }
    days_before_test_instance_cancellation { nil }
    api_ti_creation_requires_rc            { false }
    identity_provider_id                   { nil }

    # Brand api
    trait(:branded) { force_api_user_brand { true } }

    # Identity check
    trait(:id_check) { force_identity_check { true } }

    # Test mode
    trait(:test_mode) { force_test_mode { true } }

    trait :with_api_oauth_manager do
      transient do
        access_token_url { SecureRandom.urlsafe_base64(32) }
        access_token     { SecureRandom.urlsafe_base64(32) }
        login_name       { SecureRandom.urlsafe_base64(32) }
        passphrase       { SecureRandom.urlsafe_base64(32) }
      end
      after(:build) do |api_user, evaluator|
        api_user.api_oauth_manager = FactoryBot.create(:api_oauth_manager,
                                                       access_token: evaluator.access_token,
                                                       access_token_url: evaluator.access_token_url,
                                                       login_name: evaluator.login_name,
                                                       passphrase: evaluator.passphrase,
                                                       api_user: api_user)
      end
    end
  end
end
