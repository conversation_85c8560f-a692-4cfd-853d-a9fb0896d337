---
en:
  devise:
    confirmations:
      confirmed: Your account was successfully confirmed.
      hello: Hi %{name}, welcome to <PERSON><PERSON><PERSON>!
      send_instructions: You will receive an email with instructions about how to
        confirm your account in a few minutes.
      send_paranoid_instructions: If your email address exists in our database, you
        will receive an email with instructions about how to confirm your account
        in a few minutes.
      complete: Complete Registration
      accept_cgu_and_complete: I agree to the terms & conditions
      must_accept_cgu: You must accept the Terms & Policies to continue
      failure:
        invalid: Invalid Test URL
      accept_cgu: Submit
    failure:
      already_authenticated: You are already authenticated
      inactive: Your account is not activated yet.
      invalid: Invalid email or password.
      locked: Your account is temporarily locked.
      last_attempt: You have one more attempt before your account will be temporarily
        locked.
      not_found_in_database: Invalid email or password.
      timeout: Your session expired. Please sign in again to continue.
      unauthenticated: You need to sign in or sign up before continuing.
      unconfirmed: You have to confirm your account before continuing.
    mailer:
      confirmation_instructions:
        subject: "[Pipplet] Account confirmation instructions"
      recruiter_confirmation_instructions:
        subject: Pipplet language test - Instructions
      reset_password_instructions:
        subject: "[Pipplet] Reset password instructions"
      unlock_instructions:
        subject: "[Pipplet] Unlock Instructions"
    omniauth_callbacks:
      failure: Could not authenticate you from %{kind} because "%{reason}".
      success: Successfully authenticated from %{kind} account.
    passwords:
      no_token: You can't access this page without coming from a password reset email.
        If you do come from a password reset email, please make sure you used the
        full URL provided.
      send_instructions: You will receive an email with instructions about how to
        reset your password in a few minutes.
      send_paranoid_instructions: If your email address exists in our database, you
        will receive a password recovery link at your email address in a few minutes.
      updated: Your password was changed successfully. You are now signed in.
      updated_not_active: Your password was changed successfully.
    registrations:
      destroyed: Bye! Your account was successfully cancelled. We hope to see you
        again soon.
      signed_up: Welcome! You have signed up successfully.
      signed_up_but_inactive: You have signed up successfully. However, we could not
        sign you in because your account is not yet activated.
      signed_up_but_locked: You have signed up successfully. However, we could not
        sign you in because your account is locked.
      signed_up_but_unconfirmed: A message with a confirmation link has been sent
        to your email address. Please open the link to activate your account.
      update_needs_confirmation: You updated your account successfully, but we need
        to verify your new email address. Please check your email and click on the
        confirm link to finalize confirming your new email address.
      signed_up_but_user_account_has_been_disabled: Your account has been disabled.
        Please contact us for more information.
      updated: You updated your account successfully.
      forgot_password: Forgot your password?
      resend_registration: Resend registration email
      didnt_receive_unlock: Didn't receive unlock instructions?
    sessions:
      signed_in: Signed in successfully.
      signed_out: Signed out successfully.
      remember_me: Remember me
      resend_registration: Resend registration
      saml_callback:
        identity_provider:
          not_found: This login service is not configured to use Pipplet. Please contact
            your test administrator for more information
        invalid: Unsuccessful connection between the login service and Pipplet. Please
          contact your test administrator for more information. %{support_email}
        user:
          not_found: Your account is not configured to use Pipplet. Please contact
            your test administrator for more information. %{support_email}
        test_instance:
          invalid: A language test could not be created with the user information
            provided. Please contact your test administrator for more information.
            %{support_email}
      saml_metadata:
        identity_provider:
          not_found: No IdentityProvider found for sp_entity_id=%{sp_entity_id}
      new:
        sso: SSO Sign in with %{identity_provider}
    unlocks:
      send_instructions: You will receive an email with instructions about how to
        unlock your account in a few minutes.
      send_paranoid_instructions: If your account exists, you will receive an email
        with instructions about how to unlock it in a few minutes.
      unlocked: Your account has been unlocked successfully. Please sign in to continue.
    general:
      password: Password
      email: Email
      update: Update
      back_home: Back home
  errors:
    messages:
      already_confirmed: was already confirmed, please try signing in
      confirmation_period_expired: needs to be confirmed within %{period}, please
        request a new one
      expired: has expired, please request a new one
      not_found: not found
      not_locked: was not locked
      not_saved:
        one: '1 error prohibited this %{resource} from being saved:'
        other: "%{count} errors prohibited this %{resource} from being saved:"
  confirmation_mailer:
    dear: Dear %{username},
    lets_do_it: Let's do it now
    best_of_luck: Best of luck!
    pipplet_team: 'The Pipplet team - Email: <EMAIL> - Phone: +33 (0)**********.91'
    do_not_work: 'If the link above does not work, you can copy and paste this link
      in your browser to access the test:'
    send_preview:
      below_example: Below is an example of what will be sent by Pipplet to invite
        the people you add to your campaigns to take the test.
      should_you: Please feel free to contact us should you have any question.
    best_regards: Best regards,
    send_invitation:
      audition:
        unsusbscribe: Click here to stop receiving these emails.
        unsusbscribe_ok: You have been unsubscribed. You will not reveive email reminders
          anymore.
        subject_reminder: 'Reminder: You have been assigned a language test.'
        subject: Welcome to Pipplet, the test of international communication!
        client_invite_you: "%{clientname} would like you to take the Pipplet test."
        generic_invitation: You have been asked to take the Pipplet test.
        pipplet_is: 'Pipplet is an online assessment designed to demonstrate your
          ability to interact in the %{language} language. '
        taking_the_test1: 'To take the test, you will need:'
        taking_the_test2: A computer with an up to date
        taking_the_test3: Chrome
        taking_the_test4: or
        taking_the_test5: Firefox
        taking_the_test6: browser
        taking_the_test7: Ideally a
        taking_the_test8: headset with a microphone
        taking_the_test9: or a computer with speakers and a microphone
        taking_the_test10: A good internet connection
        taking_the_test11: A quiet room where you can spend 20 min alone
      subject: You have been assigned a language test
      client_invite_you: "%{clientname} would like you to take the Pipplet language
        evaluation test."
      pipplet_is: 'Pipplet is an online assessment designed to demonstrate your ability
        to interact in the %{language} language. '
      body: |
        <p>Dear %{username},</p>
        <p>%{clientname} would like you to take the Pipplet test in %{testlanguage}.</p>
        <p>Pipplet is an online assessment designed to demonstrate your oral and written skills. During the test you will be asked to <strong>speak</strong> and <strong>write</strong> in %{testlanguage} in response to various <strong>scenarios</strong>. Your answers will then be analyzed and evaluated by native language experts according to the criteria set by the Common European Framework of Reference for Languages (CEFRL).</p>
        <a href="https://help.pipplet.com/en-us/article/everything-to-know-before-taking-the-pipplet-test-1oqaf27/?1532076753365">Learn more about the Pipplet Test and take a practice test.</a>
        <p>To take the test you will need to have access to a computer with:</p>
        <ul>
        <li>an up-to-date <strong>Chrome</strong> or <strong>Firefox</strong> browser;</li>
        <li>a <strong>microphone and built-in loudspeakers</strong>, or a headset with microphone;</li>
        <li>a <strong>keyboard that is adapted to the test language</strong>. Pay special attention to accents in certain languages.</li>
        </ul>
        <p>Take the test in a quiet environment that has a <strong>good internet connection</strong>. Please make sure that you can allocate <strong>30 minutes</strong> without being interrupted.</p>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td>
              <div>
                <!--[if mso]>
                  <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="%{testlink}" style="height:36px;v-text-anchor:middle;width:240px;" arcsize="5%" strokecolor="#0084ff" fillcolor="#0084ff">
                    <w:anchorlock/>
                    <center style="color:#ffffff;font-family:Helvetica, Arial,sans-serif;font-size:14px;">Let's do it now &rarr;</center>
                  </v:roundrect>
                <![endif]-->
                <a href="%{testlink}" style="background-color:#0084ff;border:1px solid #0084ff;border-radius:3px;color:#ffffff;display:inline-block;font-family:sans-serif;font-size:14px;line-height:32px;text-align:center;text-decoration:none;width:240px;-webkit-text-size-adjust:none;mso-hide:all;">Let's do it now &rarr;</a>
              </div>
            </td>
          </tr>
        </table>
        <p>Best of luck!</p>
        <p>The Pipplet Team - Email: <a href="mailto:<EMAIL>"><EMAIL></a> - Phone: +33 (0)**********.91</p>
        <p>If the link above does not work, you can copy and paste this link in your browser to access the test:<br>%{testlink}</p>
        <br/>
        <p><a href="%{unsubscribe_url}">Click here to stop receiving these emails.</a></p>
      body_renault: |
        <p>Dear %{username},</p>
        <p>%{clientname} would like you to take the Pipplet test in %{testlanguage}.</p>
        <p>Pipplet is an online assessment designed to demonstrate your oral and written skills. During the test you will be asked to <strong>speak</strong> and <strong>write</strong> in %{testlanguage} in response to various <strong>scenarios</strong>. Your answers will then be analyzed and evaluated by native language experts according to the criteria set by the Common European Framework of Reference for Languages (CEFRL).</p>
        <a href="https://help.pipplet.com/en-us/article/everything-to-know-before-taking-the-pipplet-test-1oqaf27/?1532076753365">Learn more about the Pipplet Test and take a practice test.</a>
        <p>To take the test you will need to have access to a computer with:</p>
        <ul>
        <li>an up-to-date <strong>Chrome</strong> or <strong>Firefox</strong> browser;</li>
        <li>a <strong>microphone and built-in loudspeakers</strong>, or a headset with microphone;</li>
        <li>a <strong>keyboard that is adapted to the test language</strong>. Pay special attention to accents in certain languages.</li>
        </ul>
        <p>Take the test in a quiet environment that has a <strong>good internet connection</strong>. Please make sure that you can allocate <strong>30 minutes</strong> without being interrupted.</p>
        <p>You must take the exam independently, without external aide, or resources and/or information accessed via Internet. The recording will begin once the microphone is engaged and random surveillance measures may take place throughout the test.</p>
        <p><strong>Careful!</strong> Any attempt at exam fraud will result in an automatic rejection of your application.</p>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td>
              <div>
                <!--[if mso]>
                  <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="%{testlink}" style="height:36px;v-text-anchor:middle;width:240px;" arcsize="5%" strokecolor="#0084ff" fillcolor="#0084ff">
                    <w:anchorlock/>
                    <center style="color:#ffffff;font-family:Helvetica, Arial,sans-serif;font-size:14px;">Let's do it now &rarr;</center>
                  </v:roundrect>
                <![endif]-->
                <a href="%{testlink}" style="background-color:#0084ff;border:1px solid #0084ff;border-radius:3px;color:#ffffff;display:inline-block;font-family:sans-serif;font-size:14px;line-height:32px;text-align:center;text-decoration:none;width:240px;-webkit-text-size-adjust:none;mso-hide:all;">Let's do it now &rarr;</a>
              </div>
            </td>
          </tr>
        </table>
        <p>Best of luck!</p>
        <p>The Pipplet Team - Email: <a href="mailto:<EMAIL>"><EMAIL></a> - Phone: +33 (0)**********.91</p>
        <p>If the link above does not work, you can copy and paste this link in your browser to access the test:<br>%{testlink}</p>
        <br/>
        <p><a href="%{unsubscribe_url}">Click here to stop receiving these emails.</a></p>
      taking_the_test1: 'To take the test, you will need:'
      taking_the_test2: A computer with an up to date
      taking_the_test3: Chrome
      taking_the_test4: or
      taking_the_test5: Firefox
      taking_the_test6: browser
      taking_the_test7: Ideally a
      taking_the_test8: headset with a microphone
      taking_the_test9: or a computer with speakers and a microphone
      taking_the_test10: A good internet connection
      taking_the_test11: A quiet room where you can spend 45min alone
    two_weeks_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: Just a quick reminder to make sure you don't forget to take
        the Pipplet test. There is now 2 weeks left before the due date on %{due_date}
    one_week_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: There is now one week left to take the Pipplet test before the
        due date on %{due_date}. Taking the test will take you about one hour.
    five_days_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: There is now only 5 days left to take the Pipplet test before
        the due date on %{due_date}. Taking the test will take you about one hour.
    three_days_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: There is now only 3 days left to take the Pippplet test before
        the due date on %{due_date}. Taking the test will take you about one hour.
    two_days_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: There is now only 2 days left to take the Pipplet test before
        the due date on %{due_date}. Taking the test will take you about one hour.
    one_day_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: This is the last day to take the Pipplet test before the due
        date on %{due_date}. Taking the test will take you about one hour.
    late_reminder:
      subject: Reminder to take Pipplet, the test of international communication.
      quick_reminder: You are late on taking the Pipplet test! It was due for the
        %{due_date}. Taking the test will take you about one hour.
    first_user_completed_notice:
      subject: A candidate completed the campaign %{name}
      user_has_completed_campaign: The candidate %{user_name} has completed the campaign
        %{campaign_name}.
    all_users_completed_notice:
      subject: All candidates have completed the campaign %{name}
      users_have_completed_campaign: All the candidates have completed the campaign
        %{campaign_name}.
