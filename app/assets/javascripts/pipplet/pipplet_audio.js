var Pipplet = Pipplet || {};
Pipplet.Audio = (function () {
   "use strict";
  /*
   * Private variables
   */
  var recorder            = null;
  var isRecording         = false;
  var recordingStartedAt  = null;

  /*
   * Private methods
   */

  /*****************************************
   * Test microphone
   *****************************************/
  var requestAudioCapture = function(successCallback, refusedCallback, noAudioDeviceCallback) {

    if(!Recorder.isRecordingSupported()) {
      return noAudioDeviceCallback();
    }

    if(recorder !== null) {

      recorder.stop();
      recorder = null;
      isRecording = false;
    }

    var afterStartCallback = function() {
      Pipplet.Audio.stopRecording(0);
    };

    var errorCallback = function(error) {
      if(typeof(error) !== "undefined" && error.name == "NotFoundError") {
        noAudioDeviceCallback();
      } else {
        refusedCallback();
      }
    };

    startRecording(successCallback, successCallback, errorCallback, afterStartCallback);
  }; // requestAudioCapture



  /*****************************************
   * Record audio
   *****************************************/
  var createRecorder = function(recordingCompletedCallback, validationsFailedCallback, afterStartCallback) {
    // Create new recorder
    if(recorder == null || typeof(recorder) === undefined) {

     recorder = new Recorder({
       numberOfChannels: 1,
       encoderBitRate: 64000,
       encoderSampleRate: 16000,
       encoderPath: "/javascript/vendor/opus-recorder-4.1.0/encoderWorker.min.js",
       // Keep the stream and context around when trying to stop recording, so you can re-start without re-initializing the stream and context.
       leaveStreamOpen: !pippletHelper.isSafari() // Attention il faudra appeler recorder.clearStream() au bon moment
     });

    }

    recorder.onstart = function() {
      $("#spinner").spin(false);
      recordingStartedAt = Date.now();

      // Hacking the recording stream to run audio tests!
      // https://github.com/chris-rudmin/opus-recorder/blob/master/src/recorder.js
      // safari doeas not support AudioContext.createScriptProcessor() skip #safariSkipAudioColect
      if (!pippletHelper.isSafari()) {
        dataCollectionForAudioAnalysis(this.stream);
      }

      // Prepare afterStartCallback
      if(typeof(afterStartCallback) !== "undefined") {
        afterStartCallback(this);
      }
    };

    // Where the magic happens...
    recorder.ondataavailable = function(typedArray) {
      var dataBlob = new Blob([typedArray], { type: 'audio/ogg' });
      var url = URL.createObjectURL(dataBlob);

      var audioDuration = Math.floor((Date.now() - recordingStartedAt)/1000);
      recordingStartedAt = null;

      // Skip validation audio on safari #safariSkipAudioColect
      if(pippletHelper.isSafari() || audioAnalysis()) {
        recordingCompletedCallback(dataBlob, audioDuration);
      } else {
        validationsFailedCallback(dataBlob, audioDuration);
      }
    };
  }; // createRecorder

  var initRecording = function(recordingCompletedCallback, validationsFailedCallback, errorCallback, afterStartCallback) {
    createRecorder(recordingCompletedCallback, validationsFailedCallback, afterStartCallback);
    isRecording = true;
    recorder.start()
    .catch(function(e) {
      if(typeof(e) !== "undefined") {
        pippletHelper.debug("[Pipplet.Audio::startRecording] ERROR: " + e.name + ".  Message: " + e.message);
      } else {
        pippletHelper.debug("[Pipplet.Audio::startRecording] ERROR: Unknown");
      }
      $("#spinner").spin(false);
      errorCallback(e);
    });
  }

  var startRecording = function(recordingCompletedCallback, validationsFailedCallback, errorCallback, afterStartCallback) {
    if(isRecording === true) { return false; }
    $("#spinner").spin(true);
    if (pippletHelper.isSafari()) {
      setTimeout( function() {
        initRecording(
          recordingCompletedCallback,
          validationsFailedCallback,
          errorCallback,
          afterStartCallback
        )},
      10);
    } else {
      initRecording(
        recordingCompletedCallback,
        validationsFailedCallback,
        errorCallback,
        afterStartCallback
      );
    }
  }; // startRecording


  var stopRecording = function(){
    if(isRecording === false) { return false; }
    isRecording = false;
    recorder.stop();
  }; // stopRecording

  var removeRecorder = function() {
    if (!recorder?.stream) { return false; }
    recorder.stream.getTracks().forEach(function(track) {
      track.stop();
    });
    recorder.stop();
  }

  /*****************************************
   * Audio analysis and validation
   *****************************************/
  // Mic check variables
  var audioCheckMinimumAcceptableAverageVolume = 0.005;
  var audioCheckMaximumAcceptableAverageVolume = 1;
  var audioCheckDataPoints = 0;
  var audioCheckVolumeTotal = 0;

  var dataCollectionForAudioAnalysis = function(audioStream) {

    // Reset data points
    audioCheckDataPoints = 0;
    audioCheckVolumeTotal = 0;

    // The current method is overly simplistic.
    //
    // Suggestion of v2 to implement:
    // - https://github.com/muaz-khan/WebRTC-Experiment/blob/master/hark/hark.js
    // - https://aws.amazon.com/blogs/machine-learning/capturing-voice-input-in-a-browser/
    var createAudioMeter = function(_audioContext, clipLevel, averaging, clipLag) {
      var volumeAudioProcess = function(event) {
        var buf = event.inputBuffer.getChannelData(0);
        var bufLength = buf.length;
        var sum = 0;
        var x;

        // Compute RMS on samples
        for(var i=0; i<bufLength; i++) {
          x = buf[i];
          sum += x * x;
        }
        var rms =  Math.sqrt(sum / bufLength);
        // Smooth this out with the averaging factor applied
        // to the previous sample - take the max here because we
        // want "fast attack, slow release."
        this.volume = Math.max(rms, this.volume*this.averaging);
      }; // volumeAudioProcess

      var processor = _audioContext.createScriptProcessor(512);
      processor.onaudioprocess = volumeAudioProcess;
      processor.volume         = 0;
      processor.clipLevel      = clipLevel || 0.98;
      processor.averaging      = averaging || 0.95;
      processor.clipLag        = clipLag || 750;
      processor.connect(_audioContext.destination);
      processor.shutdown = function(){
        this.disconnect();
        this.onaudioprocess = null;
      };

      return processor;
    }; // createAudioMeter

    var dataCollectionLoop = function(time) {
      // Collect stats on audio recorded
      audioCheckDataPoints++;
      audioCheckVolumeTotal += meter.volume;

      // set up the next visual callback
      window.requestAnimationFrame(dataCollectionLoop);
    };

    // Create a new volume meter and connect it.
    var mediaStreamSource = recorder.audioContext.createMediaStreamSource(audioStream);
    var meter = createAudioMeter(recorder.audioContext);
    mediaStreamSource.connect(meter);

    // Kick off data collection
    dataCollectionLoop();
  };

  // Check whether some audio was recorded or not
  var audioAnalysis = function(e) {
    // Computing volume average for sampled data (super set of data manually recorded by the user)
    var volumeAverage;
    if (audioCheckDataPoints > 0) {
      volumeAverage = audioCheckVolumeTotal / audioCheckDataPoints;
    } else {
      volumeAverage = 0;
    }

    return (volumeAverage >= audioCheckMinimumAcceptableAverageVolume && volumeAverage <= audioCheckMaximumAcceptableAverageVolume);
  };

/*****************************************
 * Public API of Audio library
 *****************************************/
return {
  isRecording: function() {
    return (isRecording);
  },

  removeRecorder: removeRecorder,

  requestAudioCapture: function(successCallback, refusedCallback, noAudioDeviceCallback) {
    requestAudioCapture(
      // Callback if audio was successfully captured
      function() { // successCallback
        pippletHelper.trackAnalytics("test_micro_accepted");
        successCallback();
      },
      // Callback if microphone access was refused by the user
      function() { // refusedCallback
        pippletHelper.debug("[Pipplet.Audio::requestAudioCapture] User refused microphone access.");
        pippletHelper.trackAnalytics("test_micro_refused");
        refusedCallback();
      },
      // Callback if no microphone is available
      function() { // noAudioDeviceCallback
        pippletHelper.debug("[Pipplet.Audio::requestAudioCapture] User doesn't have a microphone.");
        pippletHelper.trackAnalytics("test_micro_impossible");
        noAudioDeviceCallback();
      }
    );
  },

  startRecording: function(recordingCompletedCallback, validationsFailedCallback, errorCallback, afterStartCallback) {
    startRecording(
      recordingCompletedCallback,
      validationsFailedCallback,
      function(error) { // errorCallback
        if(typeof(error) !== "undefined" && error.name == "NotFoundError") {
          pippletHelper.trackAnalytics("micro_access_impossible");
        } else {
          pippletHelper.trackAnalytics("micro_access_refused");
        }
        errorCallback();
      },
      function() { // afterStartCallback
        pippletHelper.trackAnalytics("micro_access_accepted");
        afterStartCallback();
      }
    );
  },

  stopRecording: function(delayedStop) {
    // Waiting for 1.7 sec to finish the recording properly
    if(typeof(delayedStop) === "undefined") {
      delayedStop = 1700;
    }
    setTimeout(stopRecording(), delayedStop);
  },

  getRecorder: function() {
    return(recorder);
  }

}; // return
}());
