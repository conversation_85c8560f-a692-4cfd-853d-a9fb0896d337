var Pipplet = Pipplet || {}
Pipplet.Question = function () {
  'use strict'

  /*****************************************
   * Private attributes
   *****************************************/
  // Configuration
  const questionHolder = '#question_holder'
  const endOfTestTrigger = '#endtest_holder'
  const indexPageTrigger = '.question_holder'
  const recordButtonHolder = '#record_button'
  const recorderInfoHolder = '#recorder_info'
  const submitQuestionHolder = '#next-question'
  const skipQuestionLinkHolder = '#skip-question-link'
  const skipQuestionDifficultyHolder = '#skip-question-difficulty'
  const skipQuestionTechnicalHolder = '#skip-question-technical'
  const skipQuestionShort = '#skip-short-question'
  const recordedAnswerHolder = '#recorded_answer'
  const questionStatusHolder = '#status'
  const minimumRecordingDuration = 15 // In seconds

  // Internals
  const that = this
  const finalQuestionTimeElapsed = 0
  const typingSpeed = null
  let isSendingFormData = false
  const recording = null
  let answerTooShort = false
  const NEXT_STEPS = {
    skipped: 'skipped',
    next_question: 'answered',
    timeout: 'timeout',
    leaving: 'leaving'
  }

  // Question data
  let question_id = null
  let question_type = null
  let challenge_id = null
  let challenge_linguist_id = null
  let challenge_status = null
  let question_identifier = null
  let questionDuration = 0
  let answer_type = null

  /*****************************************
   * Public attributes
   *****************************************/
  this.textAreaValue = ''
  this.nextStep = NEXT_STEPS.next_question

  /*****************************************
   * Recording button
   *****************************************/
  this.recorderButton = (function (_button) {
    const button = _button
    const api = {}

    // Public API
    api.disabled = false
    api.disable = function () {
      that.recorderButton.disabled = true
      $(button).prop('disabled', true)
    }
    api.enable = function () {
      that.recorderButton.disabled = false
      $(button).prop('disabled', false)
    }

    api.recordingInProgress = false

    api.saving = function () {
      $(button).removeClass('recording').addClass('recorded')
      $(button).prop('disabled', true)
    }

    api.recording = function () {
      $(button).removeClass('recorded').addClass('recording')
      that.recorderButton.recordingInProgress = true
    }

    // Reset to nominal state
    api.reset = function () {
      // Block recorder button for 900ms
      setTimeout(function () {
        that.recorderButton.recordingInProgress = false
        $(button).prop('disabled', false)
        $(button).removeClass('recording').addClass('recorded')
      }, 500)
    }
    return api
  }(recordButtonHolder))

  /*****************************************
   * Submit button
   * --> Set answer buttons to a "loading" states with the custom localised message
   *****************************************/
  this.submitButton = (function (_button) {
    const button = _button
    const api = {}

    api.disable = function () {
      $(button).prop('disabled', true)
    }

    api.saving = function () {
      $(button).prop('disabled', true)

      let newValue = $(button).attr('data-saving-text')
      if (newValue === null || newValue === undefined) {
        newValue = 'saving...'
      }
      $(button).attr('value', newValue)
    }

    api.recording = function () {
      $(button).prop('disabled', true)

      // var newValue = $(button).attr("data-recording-text");
      // if(newValue === null || newValue === undefined) {
      //   newValue = "recording...";
      // }
      // $(button).attr("value", newValue);
    }

    // Reset to nominal state
    api.reset = function () {
      let newValue = $(button).attr('data-default-text')
      if (newValue === null || newValue === undefined) {
        newValue = 'Submit'
      }
      $(button).attr('value', newValue)
      $(button).prop('disabled', false)
    }

    return api
  }(submitQuestionHolder))

  /*****************************************
   * Recorder contextual info
   *****************************************/
  this.recorderInfo = (function (_recorderButton, _recorderInfoHolder) {
    const button = _recorderButton
    const info = _recorderInfoHolder
    let timerStartedAt = null
    let timer = null
    const api = {}

    api.saving = function () {
      const feedback = $(button).attr('data-recorder-saving')
      $(info).html('<i>' + feedback + '</i>')

      // Stop timer.
      clearInterval(timer)
    }

    api.recording = function () {
      const feedback = $(button).attr('data-recorder-recording')
      $(info).html('<i>' + feedback + '</i>')

      const secondesTranslations = $(button).attr('data-recorder-secondes')

      // Start timer (updated every second)
      timerStartedAt = Date.now()
      timer = setInterval(function () {
        const timeSpent = Math.floor(Date.now() - timerStartedAt)
        let timeSpentString = ''
        const elt = $(info + ' .recorder_duration')

        // Determine feedback
        if (timeSpent > 60000) {
          timeSpentString = pippletHelper.millisecToMMSS(timeSpent)
        } else if (timeSpent >= 20000) {
          timeSpentString = pippletHelper.millisecToSS(timeSpent) + ' ' + secondesTranslations
          elt.addClass('time_is_good')
        } else {
          timeSpentString = pippletHelper.millisecToSS(timeSpent) + ' / 20 ' + secondesTranslations
        }
        // Update timer on screen
        elt.text(timeSpentString)
      }, 1000)
    }

    api.audioTooShort = function () {
      // var feedback = $(button).attr("data-recorder-too_short");
      // $(info).html("<i>"+feedback+"</i>");
      answerTooShort = true
    }

    api.audioValidationFailed = function () {
      const feedback = $(button).attr('data-recorder-too_low')
      $(info).html('<i>' + feedback + '</i>')
    }

    api.audioRecorded = function () {
      const feedback = $(button).attr('data-recorder-recorded')
      $(info).html("<span class='glyphicon glyphicon-ok check'></span><i>" + feedback + '</i>')
    }

    return api
  }(recordButtonHolder, recorderInfoHolder))

  /*****************************************
   * Private methods
   *****************************************/

  /*
   *
   * Load question or end-of-test templates, with the right listeners
   *
   */
  // Initialise all question elements when displaying a question (reception or production)
  const loadQuestionTemplate = function () {
    // Add listener on record button (if any)
    $(recordButtonHolder).click(function () {
      logRecordingClick(Pipplet.Test.currentQuestion.id())
      if (!that.recorderButton.recordingInProgress && !that.recorderButton.disabled) {
        startRecordingCallback()
      } else if (that.recorderButton.recordingInProgress && !that.recorderButton.disabled) { // recordingInProgress
        stopRecordingCallback()
      }
    })

    // Handle click on question SUBMIT button
    $(questionHolder).find(submitQuestionHolder).click(function () {
      // If this is true, audio recording was too short
      if (answerTooShort === true) {
        $('#skip_short_question_modal').modal('show')
        // Check if number of character is under the limit
      } else if ($('.text-count-wrapper').hasClass('under_limit') === true) {
        $('#skip_short_question_modal').modal('show')
      } else {
        // Submit question data to server
        finishQuestion()
      }
      return false
    })

    // Handle click on question SKIP Link
    $(questionHolder).find(skipQuestionLinkHolder).on('click', function (e) {
      // $('#skip_question_modal').modal('show');
    })

    // Handle click on question SKIP button in modal
    $(questionHolder).find(skipQuestionTechnicalHolder).click(function () {
      Pipplet.Test.currentQuestion.skipped()
      $('#skip_question_modal').modal('hide')
      return false
    })

    // Handle click on question SKIP button in modal
    $(questionHolder).find(skipQuestionDifficultyHolder).click(function () {
      Pipplet.Test.currentQuestion.skipped()
      $('#skip_question_modal').modal('hide')
      return false
    })

    // Handle click on question SKIP button in modal
    $(questionHolder).find(skipQuestionShort).click(function () {
      Pipplet.Test.currentQuestion.skipped()
      $('#skip_short_question_modal').modal('hide')
      return false
    })

    // Tweaks for question audio. TODO: move somewhere else...
    $(questionHolder).find('#question-audio').on('ended', function (e) {
      $(questionHolder).find('#question-audio').removeClass('playing')
    })

    // Enable play button when audio is here
    $(questionHolder).find('#play-button').click(function () {
      $(questionHolder).find('#question-audio').trigger('play')
      $(questionHolder).find('#question-audio').addClass('playing')
    })

    // Need to init it at hide because textarea is empty
    $(questionHolder).find(submitQuestionHolder).hide()

    // Disable button if textarea empty
    $('textarea.form-control').on('keyup', function () {
      if ($(this).val().length < 1) { $(questionHolder).find(submitQuestionHolder).hide() } else { $(questionHolder).find(submitQuestionHolder).show() }
    })
  } // End of loadQuestionTemplate

  /*
   *
   * Manage answers to questions
   *
   */
  var startRecordingCallback = function () {
    // Disable mic button and sumit buttons
    that.recorderButton.disable()
    that.submitButton.disable()

    // Reset answer too short
    answerTooShort = false

    // Create new recording object and start recording + register callbacks
    that.recording = new Pipplet.Recording('challenge-' + challenge_id + '-question-' + question_id)
    that.recording.startRecording(
      function (recordingUrl, audioDuration) { // successCallback
        pippletHelper.trackAnalytics('pippletQuestion_audioValidationsAndUploadToS3Success')
        recordingCompletedCallback(recordingUrl, audioDuration, true)
      },
      function (recordingUrl, audioDuration) { // validationsFailedCallback
        pippletHelper.debug('[pippletQuestion::loadQuestionTemplate][qid:' + question_id + '] audio_validation=failed s3_upload=OK')
        pippletHelper.trackAnalytics('pippletQuestion_audioValidationsFailed')

        recordingCompletedCallback(recordingUrl, audioDuration, false)
      },
      function () { // uploadFailedCallback
        pippletHelper.debug('[pippletQuestion::loadQuestionTemplate][qid:' + question_id + '] Error while uploading audio to S3')
        pippletHelper.trackAnalytics('pippletQuestion_audioUploadToS3Failed')
        that.submitButton.reset()
        that.recorderButton.reset()
        // Restart timer
        Pipplet.Test.timerRestart()
        // TODO: ask the user to record again
      },
      function () { // errorCallback
        pippletHelper.debug('[pippletQuestion::loadQuestionTemplate][qid:' + question_id + '] Something went wrong during the audio recording')
        pippletHelper.trackAnalytics('pippletQuestion_unkownAudioError')
        that.submitButton.reset()
        that.recorderButton.reset()
        // Restart timer
        Pipplet.Test.timerRestart()
        // TODO: show feedback to user
      },
      function () { // afterStartCallback
        // This first call also sets recordingInProgress to true
        that.recorderButton.enable()
        that.recorderButton.recording()
        that.submitButton.recording()
        that.recorderInfo.recording()
      }
    ) // recording.startRecording
  }

  // Called when the recorder is stopped
  //   - manually by clicking on the button
  //   - automatically during timeout
  //   - automatically when the user is closing the page
  var stopRecordingCallback = function () {
    // Pause the timer
    Pipplet.Test.timerStop()

    // Stop audio recording and trigger callbacks defined above in that.recording.startRecording
    Pipplet.Recording.stop()

    // This first call also sets recordingInProgress to false after a small delay to prevent double clicking issues.
    that.recorderButton.saving()
    that.recorderInfo.saving()
    that.submitButton.disable()
  }

  // Called when a recording has been fully saved, and validated.
  var recordingCompletedCallback = function (recordingUrl, audioDuration, validationPassed) {
    pippletHelper.debug('[pippletQuestion::recordingCompletedCallback][qid:' + question_id + '] audioDuration: ' + audioDuration + 's. url: ' + recordingUrl)

    $(questionHolder).find(submitQuestionHolder).show()

    // Duration too short
    if (audioDuration < minimumRecordingDuration) {
      that.recorderInfo.audioTooShort()
    }
    //  saveAudioAnswer(recordingUrl);

    // TODO: re-enable this until we have a good UX spec for it.
    // Volume too low
    // } else if(!validationPassed) {
    //   that.recorderInfo.audioValidationFailed();

    // Seems ok
    // } else {
    that.recorderInfo.audioRecorded()
    saveAudioAnswer(recordingUrl)
    that.submitButton.reset()
    // }

    // Making sure that we finalize the audio recording properly in case of timeout or page closing.
    if (that.nextStep == NEXT_STEPS.timeout || that.nextStep == NEXT_STEPS.leaving) {
      // Keep buttons blocked and timer stopped
      sendAnswerToServer()
    } else {
      // Get buttons ready for another recording
      that.recorderButton.reset()
      // Restart timer
      Pipplet.Test.timerRestart()
    }
  }

  // Save the audio URL to the right form element
  var saveAudioAnswer = function (url) {
    $(recordedAnswerHolder).attr('value', url)
  }

  // Manage validation errors sent from the server
  const handleInvalidAnswer = function (status) {
    pippletHelper.debug('[pippletQuestion::handleInvalidAnswer][qid:' + question_id + '] Status (below)')
    pippletHelper.debug(status)

    jQuery.each(status.validation_errors, function (error) {
      // Question id is the error key
      const question_id = error

      pippletHelper.debug('[pippletQuestion::handleInvalidAnswer][qid:' + question_id + "][looping on errors] Content of element '.question ." + question_id + "'")
      pippletHelper.debug(status.validation_errors[question_id])

      $('.question.' + question_id).addClass('validation_error')

      // Test if question is a textarea. If so placement will be on the right
      let error_placement
      if ($('.question.' + question_id).find('textarea').length > 0) {
        error_placement = 'top'
      } else {
        error_placement = 'bottom'
      }

      $('.question.' + question_id).tooltip({
        title: status.validation_errors[question_id].join(' '),
        placement: error_placement,
        container: $('.question.' + question_id)
      })
    }) // jQuery.each(status.validation_errors...

    // Show feedback
    $('.question.validation_error').tooltip('show')
    // Restart timer
    Pipplet.Test.timerRestart()
    // Re-enable submit button
    that.submitButton.reset()
    that.recorderButton.reset()
  }

  var logRecordingClick = function (questionId) {
    const data = {
      question_id: questionId
    }

    $.ajax({
      type: 'POST',
      dataType: 'json',
      async: true,
      url: '/event_logs/log_microphone_click_for_ti',
      data
    })
  }

  // Send answers to server
  var sendAnswerToServer = function () {
    // Stop if a sendAnswerToServer for this question is being executed, if not set a flag on currentQuestion that a sendAnswerToServer is being executed and pursue
    if (isSendingFormData === true) {
      pippletHelper.debug('[pippletQuestion::sendAnswerToServer][qid:' + question_id + '] STOP! Multiple Send for Data for currentQuestion')
      return false
    } else {
      isSendingFormData = true
    }

    // Send Metadata to the server
    that.sendMetadata()

    // Transmit question status to server
    $(questionStatusHolder).val(that.nextStep)
    const parameters = $(questionHolder + ' .question-form').serializeArray()

    // If we are calling this function from the beforeunload event (user wants to leave the page), make the request synchronous so we wait for the result
    let asyncStatus = true
    if (that.nextStep === NEXT_STEPS.leaving) {
      asyncStatus = false
    }

    // Remove all tooltip and validation errors before sending the request
    $('.question.validation_error').tooltip('destroy')
    $('.question.validation_error').removeClass('validation_error')

    $.ajax({
      type: 'POST',
      dataType: 'json',
      async: asyncStatus,
      url: '/questions',
      data: $.param(parameters), // serializes the form's elements.
      success: function (data) {
        isSendingFormData = false
        switch (that.nextStep) {
          case NEXT_STEPS.timeout:
            Pipplet.Test.displayTimeoutMessage()
            break
          case NEXT_STEPS.skipped:
            Pipplet.Test.nextQuestion()
            break
          case NEXT_STEPS.next_question:
            Pipplet.Test.nextQuestion()
            break
          case NEXT_STEPS.leaving:
            break
        }

        // that.submitButton.reset();
      },
      error: function (status) {
        isSendingFormData = false
        that.submitButton.reset()

        pippletHelper.debug('[pippletQuestion::sendAnswerToServer][qid:' + question_id + '][AJAX] Error callback (displaying status below)')
        pippletHelper.debug(status)

        if (that.nextStep == NEXT_STEPS.timeout) {
          Pipplet.Test.displayTimeoutMessage()
        } else if (that.nextStep == NEXT_STEPS.leaving) {
          // Nothing to do...

        } else if (typeof status.responseJSON !== 'undefined') {
          if (status.responseJSON.error_status == 'NOT VALID') {
            handleInvalidAnswer(status.responseJSON)
          } else if (status.responseJSON.error_status == 'BAD REQUEST') {
            pippletHelper.debug('[pippletQuestion::sendAnswerToServer][qid:' + question_id + '][AJAX] Error callback / Question validation error : Bad request')
          }
        } else {
          pippletHelper.debug('[pippletQuestion::sendAnswerToServer][qid:' + question_id + '][AJAX] Error callback / Question validation error : Default action')
        }
      }
    }) // $.ajax
  } // sendAnswerToServer

  // This is the standard "Next question" action
  var finishQuestion = function () {
    // Extract written answer
    that.textAreaValue = $('.question textarea.form-control').val()

    // Stop timer and record actual time elapsed
    Pipplet.Test.timerStop()
    that.finalQuestionTimeElapsed = questionDuration * Pipplet.Test.timerElapsedPercentage()

    // Making sure that we finalize the audio recording properly in case of timeout or page closing.
    // sendAnswerToServer will be called from a different location
    if (that.recorderButton.recordingInProgress && (that.nextStep == NEXT_STEPS.timeout || that.nextStep == NEXT_STEPS.leaving)) {
      stopRecordingCallback()
    } else {
      // Prevent double click on buttons
      that.submitButton.saving()
      that.recorderButton.saving()
      // Hide any modal so they don't stay
      $('.modal').modal('hide')
      sendAnswerToServer()
    }
  }

  /*****************************************
   * Public methods
   *****************************************/

  // Get question data from backend and get ready to display it
  this.loadQuestionData = function (questionLoadedCallback) {
    $.ajax({
      type: 'GET',
      url: '/questions/retrieve',
      success: function (data) {
        // Get variables about the question from the page so we can pass them to the uploader
        question_id = $(data).filter('meta[name=question_id]').attr('content')
        question_type = $(data).filter('meta[name=question_type]').attr('content')
        challenge_id = $(data).filter('meta[name=challenge_id]').attr('content')
        challenge_linguist_id = $(data).filter('meta[name=challenge_linguist_id]').attr('content')
        challenge_status = $(data).filter('meta[name=challenge_status]').attr('content')
        question_identifier = $(data).filter('meta[name=question_identifier]').attr('content')
        questionDuration = parseInt($(data).filter('meta[name=question_duration]').attr('content'))
        answer_type = $(data).filter('meta[name=answer_type]').attr('content')

        $('#alert_messages').hide()
        $(questionHolder).empty().append(data)
        pippletHelper.applyJsTweaks()
        pippletHelper.checkPlayerChange()

        // It looks like we are displaying the index page, not good! Let's redirect to it.
        if ($(indexPageTrigger).length > 1) {
          Pipplet.Test.removeWindowListeners()
          window.location = '/questions'

          // It looks like we are displaying the end test page
        } else if ($(endOfTestTrigger).length > 0) {
          pippletHelper.trackAnalytics('end_test')
          Pipplet.Test.triggerEndofTest()

          // It looks like we are displaying a question
        } else {
          pippletHelper.trackAnalytics('question')
          loadQuestionTemplate()
        }

        // Don't put anything after this callback
        questionLoadedCallback(question_id)
      },
      error: function (status) {
        pippletHelper.debug('[pippletQuestion::loadQuestionData][qid:' + question_id + '] error while loading question data')
        pippletHelper.debug(status)
      }
    })
  } // End loadQuestionData

  this.getQuestionDuration = function () {
    return questionDuration
  }

  this.timeout = function () {
    that.nextStep = NEXT_STEPS.timeout
    finishQuestion()
  }

  this.skipped = function () {
    that.nextStep = NEXT_STEPS.skipped
    finishQuestion()
  }

  this.leaveTest = function () {
    that.nextStep = NEXT_STEPS.leaving
    finishQuestion()
  }

  this.id = function () {
    return question_id
  }

  this.sendMetadata = function () {
    const metadataObject = {
      typing_speed: that.typingSpeed
    }

    let is_empty = true
    Object.keys(metadataObject).forEach(function (key) {
      if ((metadataObject[key] !== undefined) && (metadataObject[key] !== null)) {
        is_empty = false
      }
    })

    if (is_empty == true) {
      return
    }

    $.ajax({
      type: 'POST',
      dataType: 'json',
      async: true,
      url: '/productions/' + question_id + '/set_metadata',
      data: metadataObject,
      success: function (data) {}
    })
  }
}
