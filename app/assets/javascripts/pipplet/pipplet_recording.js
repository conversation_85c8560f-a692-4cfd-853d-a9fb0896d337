var Pipplet = Pipplet || {};
Pipplet.Recording = function(_label) {
   "use strict";
  /*
   * Private attributes
   */
  var that     = this;
  var STATUSES = [
    'initialised',
    'saved_locally',
    'tested',
    'uploaded'
  ];

  /*
   * Public attributes
   */
  this.id        = 0;
  this.label     = _label || '';
  this.data      = null;
  this.status    = STATUSES[0];
  this.localUrl = null;
  this.remoteUrl = null;

  /*
   * Private methods
   */
  var getObject = function(callback) {
    Pipplet.Storage.get(
      Pipplet.Storage.stores.recordings,
      that.id,
      function(object) {
        callback(object);
      }
    );
  }; // getObject

  var getRecordingBlob = function(callback) {
    getLocalUrl(function(localUrl) {
      that.localUrl = localUrl;

      var xhr = new XMLHttpRequest();
      xhr.open("GET", localUrl, true);
      xhr.responseType = "blob";
      xhr.addEventListener("load", function() {
        if (xhr.status === 200) {
          callback(xhr.response);
        } else {
          pippletHelper.debug("[Pipplet.Recording::getRecordingBlob][AJAX][label:"+that.label+"] error: " + xhr.response);
        }
      }, false);
      xhr.addEventListener("error", function() {
        pippletHelper.debug("[Pipplet.Recording::getRecordingBlob][AJAX][label:"+that.label+"] error: " + xhr.response);
      }, false);
      xhr.send();

    }); // getLocalUrl
  }; // getRecordingBlob


  var saveLocally = function(data, saveLocallyCallback) {
    var recordingHash = {
      label: that.label,
      recording: data,
      status: STATUSES[1]
    };

    Pipplet.Storage.put(
      Pipplet.Storage.stores.recordings,
      recordingHash,
      function(key) {
        that.id = key;
        that.status = STATUSES[1];

        saveLocallyCallback();
      }
    );
  }; // saveLocally


  var getLocalUrl = function(callback) {
    getObject(function(object){
      callback(URL.createObjectURL(object.recording));
    });
  }; // getLocalUrl


  /*
   * Public methods
   */
  this.uploadToS3 = function(uploadSuccessCallback, uploadFailedCallback) {
    getRecordingBlob(function(recordingBlob) {

      // Get AWS signed query for data upload
      $.ajax({
        type: "GET",
        url: "/questions/s3_uploader?label=" + that.label,
        dataType: "json",
        success: function(response) {

          var uploadId = response.id;

          // Create file element
          var fileInput = document.createElement("input");
          fileInput.setAttribute("style", "display: none;");
          fileInput.setAttribute("id", uploadId);
          fileInput.setAttribute("type", "file");
          fileInput.setAttribute("multiple", "");
          fileInput.setAttribute("data-url", response.url);
          fileInput.setAttribute("name", "helloaws");
          document.body.appendChild(fileInput);

          // https://github.com/blueimp/jQuery-File-Upload/wiki/Basic-plugin
          // https://devcenter.heroku.com/articles/direct-to-s3-image-uploads-in-rails
          $('#'+uploadId).fileupload({
            url:              response.url,
            type:             'POST',
            autoUpload:       true,
            formData:         response.data,
            paramName:        'file', // S3 does not like nested name fields i.e. name="user[avatar_url]"
            dataType:         'XML',  // S3 returns XML if success_action_status is set to 201
            replaceFileInput: false,
            singleFileUploads: true,

            done: function (e, data) {

                            // extract key and generate URL from response
              var key   = $(data.jqXHR.responseXML).find("Key").text();
              var url   = 'https://' + response.host + '/' + key;
              uploadSuccessCallback(url);

              // Update recording.status
              that.status = STATUSES[3];
            },
            fail: function(e, data) {
              pippletHelper.debug("[Pipplet.Recording::uploadToS3][AJAX][label:"+that.label+"] Upload to S3: error");
              pippletHelper.debug(data.jqXHR.responseXML);
              uploadFailedCallback();
            }
          }); // fileupload

          // Trigger upload
          $('#'+uploadId).fileupload('add', { files: [recordingBlob]});


        },
        error: function(response) {
          pippletHelper.debug("[Pipplet.Recording::uploadToS3][AJAX][label:"+that.label+"] Get AWS signed data: error: " + response);
        }
      }); // ajax

    }); // getRecordingBlob

  };

  this.startRecording = function(successCallback, validationsFailedCallback, uploadFailedCallback, errorCallback, afterStartCallback) {
    // Start recording audio and callback for recording data
    Pipplet.Audio.startRecording(function(data, audioDuration) {

      // If audio passed automated tests, save recording to IndexDB, upload to S3 and get URL
      saveLocally(data, function() {
        that.uploadToS3(function(remoteUrl) {
          that.remoteUrl = remoteUrl;
          successCallback(remoteUrl, audioDuration);
        }, uploadFailedCallback); // recording.uploadToS3
      }); // recording.saveLocally
    },

    // If audio didn't pass automated tests...
    function(data, audioDuration) {
      pippletHelper.debug("[Pipplet.Recording::startRecording][validation_failed][label:"+that.label+"] Audio URL in memory " + URL.createObjectURL(data));

      // Save recording to IndexDB, just in case (for now).
      saveLocally(data, function() {
        that.uploadToS3(function(remoteUrl) {
          pippletHelper.debug("[Pipplet.Recording::startRecording][validation_failed][label:"+that.label+"] Audio URL from S3: " + remoteUrl);
          that.remoteUrl = remoteUrl;
          validationsFailedCallback(remoteUrl, audioDuration);
        }, uploadFailedCallback); // recording.uploadToS3
      }); // recording.saveLocally
    },

    // Generic callback if something goes wrong
    errorCallback,

    // Custom callbacks after recording has started!
    afterStartCallback
    ); // Pipplet.Audio.startRecording
  };

  this.stopRecording = function() {
    Pipplet.Recording.stop();
  };

};

Pipplet.Recording.stop = function() {
  Pipplet.Audio.stopRecording();
};
