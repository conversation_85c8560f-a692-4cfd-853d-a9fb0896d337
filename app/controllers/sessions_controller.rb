class SessionsController < Devise::SessionsController
  skip_before_action :verify_authenticity_token, only: [:saml_callback, :saml_logout]
  before_action :define_identity_provider, only: [:saml_callback, :saml_metadata, :saml_logout]
  before_action :parse_saml_response, only: [:saml_callback]
  before_action :create_user_from_response, only: [:saml_callback]

  private

  def define_identity_provider
    return if params[:sp_entity_id].blank?

    @identity_provider = IdentityProvider.active.find_by(sp_entity_id: params[:sp_entity_id])
  end

  def parse_saml_response
    return unless @identity_provider

    @response = OneLogin::RubySaml::Response.new(params[:SAMLResponse], settings: @identity_provider.saml_settings, **assertion_response_options)
    collect_errors = true
    unless @response.is_valid?(collect_errors)
      Alert.sso(identity_provider: @identity_provider,
                meta: { response: @response&.response },
                message: @response.errors&.join(', '))
      sign_out(current_user) if user_signed_in?
      redirect_to root_url, alert: t('devise.sessions.saml_callback.invalid', support_email: @identity_provider.sso_user&.support_email)
    end
  end

  def create_user_from_response
    return unless @identity_provider.present? &&
                  @identity_provider.sso_user.present? &&
                  @identity_provider.create_ti_on_first_sign_in?

    email = IdentityProvider.extract_email_from_response(@response)

    return if @identity_provider.sso_user.associated_user_with_email?(email)

    first_name = @response.attributes[@identity_provider.first_name_saml_attribute]
    last_name = @response.attributes[@identity_provider.last_name_saml_attribute]
    order_reference = @response.attributes[@identity_provider.order_reference_saml_attribute]
    order_information = @response.attributes.to_h
    user = @identity_provider.sso_user.create_test_instance(first_name:,
                                                            last_name:,
                                                            email:,
                                                            order_reference:,
                                                            order_information:)
    user.update!(identity_provider: @identity_provider)
  rescue StandardError => e
    Alert.sso(identity_provider: @identity_provider,
              meta: { first_name:,
                      last_name:,
                      email:,
                      response: @response&.response },
              message: "Error while creating a first TestInstance, error: #{e.message}")
    sign_out(current_user) if user_signed_in?
    redirect_to root_url, alert: t('devise.sessions.saml_callback.test_instance.invalid', support_email: @identity_provider.sso_user.support_email)
  end

  public

  def new
    @identity_providers = IdentityProvider.active

    super
  end

  def assertion_response_options
    idp_options = {}

    # we can put any nasty vendor work arounds here
    idp_options = { skip_subject_confirmation: true } if @identity_provider.vendor_one_login?

    {
      allowed_clock_drift: 5.seconds
    }.merge(idp_options).merge(@identity_provider.assertion_response_options.symbolize_keys)
  end

  def saml_callback
    return redirect_to root_url, alert: t('devise.sessions.saml_callback.identity_provider.not_found') unless @identity_provider

    user = User.find_by(email: @response.name_id&.downcase, identity_provider: @identity_provider)
    user = User.find_by(email: @response.attributes[:email]&.downcase, identity_provider: @identity_provider) if user.nil? && @response.attributes[:email]

    # TODO: temporary SSO debug to remove
    if user.nil?
      Alert.sso(identity_provider: @identity_provider,
                meta: { response: @response&.response },
                message: 'User not found (TI/User failed to create silently or inconsistency in email address)')
    end

    return redirect_to root_url, alert: t('devise.sessions.saml_callback.user.not_found', support_email: @identity_provider.sso_user&.support_email) if user.nil? || user.is_admin? || user.is_examiner?

    user.confirm unless user.confirmed?
    sign_in(user)
    redirect_to after_sign_in_path_for(user)
  end

  # See https://github.com/onelogin/ruby-saml#service-provider-metadata
  def saml_metadata
    raise ActionController::RoutingError, t('devise.sessions.saml_metadata.identity_provider.not_found', sp_entity_id: params[:sp_entity_id]) unless @identity_provider

    settings = @identity_provider.saml_settings
    meta = OneLogin::RubySaml::Metadata.new
    render xml: meta.generate(settings), content_type: 'application/samlmetadata+xml'
  end

  # TODO: log out
  # override the destroy method, and do special single logout stuff if they have it configured
  # def destroy
  # end

  # TODO: log out
  # Trigger SP and IdP initiated Logout requests
  def saml_logout; end

  # TODO: log out
  def respond_to_on_destroy
    # if we are doing single logout (SLO) don't redirect,
    # the saml_sp_logout_request function handles the redirect
    super unless session[:transaction_id]
  end

  # TODO: log out
  # Method to handle IdP initiated logouts
  def saml_idp_logout_request; end

  # TODO: log out
  # Create a SP initiated SLO
  def saml_sp_logout_request(idp_config, user); end

  # TODO: log out
  def saml_process_logout_response; end
end
