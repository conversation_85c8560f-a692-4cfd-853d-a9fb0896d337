class Admin::SsoUsersController < Admin::AdminController
  include Restful<PERSON><PERSON>roller

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :sso_user
  end

  def identifier
    :name
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :name,
      :default_locale,
      :default_test_language,
      :default_test_profile_id,
      :force_test_mode,
      :default_client_name,
      :source,
      :support_email
    ]
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # PUT /admin/resources/1/activate
  # PUT /admin/resources/1/activate.json
  def activate
    respond_to do |format|
      if @resource.activate!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/expire
  # PUT /admin/resources/1/expire.json
  def expire
    respond_to do |format|
      if @resource.expire!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end
end
