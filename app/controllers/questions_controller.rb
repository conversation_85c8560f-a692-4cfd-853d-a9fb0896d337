# == Schema Information
#
# Table name: questions
#
#  id             :integer          not null, primary key
#  layout_zone_id :integer
#  challenge_id   :integer
#  order          :integer
#  type           :string
#  question_for   :integer
#  label          :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  deleted_at     :datetime
#

class QuestionsController < ApplicationController
  # Skip CSRF verification for the wheebox callback
  skip_before_action :verify_authenticity_token, only: [:wheebox_callback]
  # Custom token based authentication
  before_action :authenticate_user_from_token!, except: [:wheebox_callback]
  # Devi<PERSON>'s authentication
  before_action :authenticate_user!, except: [:wheebox_callback]
  # Redirect user to confirmation page if T&Cs have not been accepted
  before_action :ensure_terms_and_policies_are_accepted

  # ----------------------------------------
  # :section: Index/Retrieve: the starting point of test workflow
  # ----------------------------------------
  # Display the test starting page. Starts the test with the Pipplet.Test JS object.
  def index
    test_instance = current_user.test_instances.not_completed.find_by(uuid: params[:uuid]) || current_user.current_test_instance
    return redirect_to choose_test_url(current_user) unless test_instance

    current_user.update(current_ti: test_instance.id)
    redirect_to choose_test_url(current_user) if current_user.current_test_instance_questions.length.zero?

    @enable_wheebox_proctoring = test_instance&.client_config&.enable_wheebox_proctoring || false

    if @enable_wheebox_proctoring
      begin
        user_id = current_user.uuid
        event_id = "pipplet" # TODO: get event id
        first_name = current_user.first_name
        attempt_number = 1 # TODO: get attempt number
        email = current_user.email

        registration_service = WheeboxApi.new
        registration_response = registration_service.register_candidate(
          user_id,
          event_id,
          first_name,
          attempt_number,
          email
        )
        @registration = registration_response[:data]
        @token = registration_response[:token]

        if @registration && @registration['attemptId']
          # Store the Wheebox attemptId on the test_instance
          if test_instance.update(wheebox_attempt_id: @registration['attemptId'].to_s)
            EventLog.add(test_instance, :question, 'Wheebox registration successful for TestInstance', :info)
          else
            EventLog.add(test_instance, :question, 'Wheebox registration successful but failed to save attemptId for TestInstance', :warning)
            flash.now[:alert] = "Proctoring service was enabled, but there was an issue saving session details. Proctoring might not be fully active."
          end
        else
          EventLog.add(test_instance, :question, 'Wheebox registration response missing attemptId', :warning)
          raise WheeboxApi::WheeboxError, "Wheebox registration failed: attemptId not received."
        end
      rescue WheeboxApi::WheeboxError => e
        EventLog.add(test_instance, :question, 'Wheebox API error during registration', :warning)
        flash.now[:alert] = "We couldn't set up the proctoring service at this time. Please try again later or contact support if the issue persists."
        @enable_wheebox_proctoring = false
        @registration = nil
        @token = nil
      end
    end

    # Log browser data for all paths while test taking for each session
    begin
      EventLog.add(
        test_instance,
        :browser_details,
        log_data = {
          session_id: session['session_id'],
          browser_name: browser.name,
          full_version: browser.full_version,
          platform: browser.platform.name,
          device: browser.device.name,
          user_agent: request.user_agent
        }.to_json,
        :info
      )
    rescue StandardError => e
      Alert.system("browser_details EventLog could not be added", e.message, meta: {
                     test_instance_id: test_instance.id
                   })
    end
  end

  def retrieve
    # @current_questionable is either a Production or Reception object
    @current_questionable = current_user.next_questionable
    layout = request.xhr? ? false : 'application'
    if @current_questionable
      # Assign user & test_instance to this questionable
      @current_questionable.assign_user_and_test_instance(current_user, current_user.current_test_instance)
      # Update status of current questionable
      @current_questionable.do_being_answered!

      # Update status of test_instance:
      # - if this is the first question: status = in_progress
      current_user.current_test_instance.start_test! if current_user.current_test_instance.may_start_test?

      # Extracts some stats for the view
      @ratio_of_required_questions_answered = current_user.current_test_instance.ratio_of_required_questions_answered
      @count_of_questions_answered = current_user.current_test_instance.count_of_questions_answered
      @count_of_questions_to_answer = current_user.current_test_instance.count_of_questions_to_answer

      # Render template
      # Note: for now we only accept rendering from XMLHttpRequest to avoid confusion
      if request.xhr?
        EventLog.add(current_user.current_test_instance, :question, "Rendering Questionable 'id'=#{@current_questionable.id}", :info)
        render template: @current_questionable.layout.template_name, layout: layout
      else
        # render text: 'You are not allowed to access this page directly.'
        EventLog.add(current_user.current_test_instance, :question, 'Redirecting to choose test', :info)
        redirect_to choose_test_url(current_user)
        nil
      end

    else
      EventLog.add(current_user.current_test_instance, :question, 'Current Questionable is nil', :info)
      # This could mean 2 things
      #   1- Either no more productions are available
      #   2- Or user has answered all required questions
      if current_user.has_answered_enough_questions?
        # Update status of Test Instance (and trigger callback of End of Test)
        EventLog.add(current_user.current_test_instance, :question, 'Current user has answered enough questions', :info)
        if current_user.current_test_instance.may_complete?
          # Call Wheebox end_test if proctoring was enabled and attempt_id is present
          completed_test_instance = current_user.current_test_instance

          EventLog.add(current_user.current_test_instance, :question, 'Test Instance Completed. End of test.', :info)
          current_user.current_test_instance.complete!

          if completed_test_instance&.client_config&.enable_wheebox_proctoring && completed_test_instance.wheebox_attempt_id.present?
            begin
              # Call the API
              wheebox_response = WheeboxApi.new.end_test(completed_test_instance.wheebox_attempt_id)

              EventLog.add(current_user.current_test_instance, :question, 'Wheebox end_test call successful', :info)
            rescue WheeboxApi::WheeboxError => e
              EventLog.add(current_user.current_test_instance, :question, 'Wheebox end_test call failed', :warning)
              flash.now[:warning] = "Your test is complete, but there was a minor issue syncing with the proctoring service."
            end
          else
            EventLog.add(current_user.current_test_instance, :question, 'Wheebox Proctoring not enabled for this test instance', :warning)
          end
        else
          EventLog.add(current_user.current_test_instance, :question, 'Test Instance May not complete! End of test.', :warning)
        end
        render_end_of_test
      else
        EventLog.add(current_user.current_test_instance, :question, 'Current User has not answered enuough question - Error managment...', :info)
        # Damage control before showing an error message...
        if current_user.current_test_instance && current_user.current_test_instance.next_questionables.empty?
          EventLog.add(current_user.current_test_instance, :question, 'Test Instance no next questionnables!', :warning)
          # if no questions on first try, we try to regenerate les NQ
          EventLog.add(current_user.current_test_instance, :question, 'Trying to update next questionnables...', :warning)
          current_user.current_test_instance.update_next_questionables
          # if we could get some NQ, great!
          if !current_user.current_test_instance.next_questionables.empty?
            # All is good, let's try again
            EventLog.add(current_user.current_test_instance, :question, 'Update next questionnables successfull!', :warning)
            render 'index', layout: layout
            # if still no NQ, and there is another test_instance ready, abandon current TI and display start page of new one:
          elsif current_user.current_test_instance.next_questionables.empty? && current_user.has_next_test_instance?
            EventLog.add(current_user.current_test_instance, :question, 'Update next questionnables failed! Skipping test instance with index!', :danger)
            current_user.skip_test_instance!
            render 'index', layout: layout
          else
            EventLog.add(current_user.current_test_instance, :question, 'Update next questionnables failed! Skipping test instance with no_more_question!', :danger)
            render 'no_more_question', layout: layout
          end

        else
          EventLog.add(current_user.current_test_instance, :question, 'Current User has no test instance or next_questionables is not empty', :warning)
          EventLog.add(current_user.current_test_instance, :question, 'Current User.current_test_instance is', :warning)
          EventLog.add(current_user.current_test_instance, :question, current_user.current_test_instance.to_yaml, :warning)
          current_user.current_test_instance || current_user.test_instances.last
          render_end_of_test
        end
      end
    end
  end

  # ----------------------------------------
  # :section: Managing test events
  # ----------------------------------------
  # Display end of test page
  def end_of_test
    render_end_of_test
  end

  private

  def render_end_of_test
    layout = request.xhr? ? false : 'application'

    @last_test_instance = current_user.previous_test_instance
    @next_test = current_user.current_test_instance

    @next_test&.update(skip_tutorial: true)
    render 'end_of_test', layout: layout
  end

  public

  # Display time-out page
  def timeout
    layout = request.xhr? ? false : 'application'
    render 'timeout', layout: layout
  end

  # GET /questions/s3_uploader
  def s3_uploader
    foldername = if current_user
                   if current_user.current_test_instance
                     "u-#{current_user.id}-ti-#{current_user.current_test_instance.id}"
                   else
                     "u-#{current_user.id}-no-ti"
                   end
                 else
                   SecureRandom.uuid
                 end

    filename = "#{Time.now.strftime('%Y%m%dT%H%M%S')}--"
    filename += if params[:label] && !params[:label].empty?
                  params[:label]
                else
                  SecureRandom.uuid
                end

    s3_direct_post = Rails.application.config.s3[:bucket].presigned_post(
      key: "recordings/#{foldername}/#{filename}.ogg",
      success_action_status: '201',
      acl: 'public-read',
      content_type: 'audio/ogg'
    )

    response = {
      id: SecureRandom.uuid,
      url: s3_direct_post.url,
      host: URI.parse(s3_direct_post.url).host,
      data: s3_direct_post.fields
    }

    respond_to do |format|
      format.html { render json: response, layout: false }
      format.js { render json: response, layout: false }
    end
  end

  # ----------------------------------------
  # :section: Saving answers
  # ----------------------------------------
  # POST /questions
  def record
    @current_questionable = current_user.next_questionable

    case params['type']
    when 'Production'
      save_question('production', Production, params)
      EventLog.add(current_user, :question, "production: #{params}", :info)
    when
      save_question('reception', Reception, params)
      EventLog.add(current_user, :question, "reception: #{params}", :info)
    else
      EventLog.add(current_user, :question, "Unknown type: #{params}", :info)
      redirect_to action: :index
    end
  end

  def alert_admin_time_out
    ti = TestInstance.find params[:test_instance_id]

    # msg = "Assets could not be load, test ended\n"
    begin
      # ti.log("[Asset Load Timeout]", nil)
      EventLog.add(ti, :process, '[Asset Load Timeout]', :warning)
      @current_questionable = current_user.current_questionable
      @current_questionable.technical_error!
      # msg += 'Questionable has been set as technical error'
    rescue StandardError
      # Ignored
      # msg += "Questionable couldn't be set as technical error"
    end

    render json: :ok
  end

  # Handle the POST callback from Wheebox after face training
  def wheebox_callback
    token = params[:token] # From current_user.confirmation_token

    unless token
      EventLog.add(nil, :question, 'Invalid or expired Wheebox callback token', :warning)
      redirect_to root_path, alert: "Session expired. Please start your test again."
      return
    end

    redirect_to user_confirmation_url(confirmation_token: token)
  rescue StandardError => e
    EventLog.add(test_instance, :question, "Wheebox callback error: #{e.message}", :error)
    redirect_to root_path, alert: "An error occurred. Please start your test again."
  end

  private

  def save_question(questionable_string, questionable_class, params)
    EventLog.add(current_user, :question, 'save_question', :warning)
    # Get questionable object
    @current_questionable = current_user.current_questionable
    EventLog.add(current_user, :question, "Current Questionable #{@current_questionable.to_yaml}", :warning)

    # Register time to answer question
    @current_questionable&.answered_now!

    # If we are dealing with a non-standard status
    if params['status'] && params['status'] == 'answered'
      save_question_answered(@current_questionable, questionable_string, questionable_class, params)
    else
      save_question_edge_cases(@current_questionable, questionable_string, questionable_class, params)
    end
  end

  def save_question_answered(current_questionable, questionable_string, questionable_class, params)
    @current_questionable = current_questionable

    ### User is trying to misuse the application (cheating attempt) ###
    # Multi-clicks on submit button also fall under this category
    if !@current_questionable || !@current_questionable.is_a?(questionable_class) ||
       params[questionable_string].nil? || params[questionable_string]['id'].nil? ||
       params[questionable_string]['id'] != @current_questionable.id.to_s

      # Flag questionable as cheating attempt, compute next challenge
      # Note that multi-clicks on submit button also fall under this category. So to avoid that, we leave
      # questionable with the same user_id as the current user untouched. The request is just
      if @current_questionable &&
         (@current_questionable.user_id.nil? || @current_questionable.user_id != current_user.id)

        @current_questionable.cheating_attempt!
      end

      # if @current_questionable
      #   msg = "Questionable: #{@current_questionable.class.name}##{@current_questionable.id} \nUser: #{current_user.id}"
      #   msg += "\n\n============ Current Questionable ============\n"
      #   msg += @current_questionable.to_yaml
      # else
      #   msg = '@current_questionable is nil'
      # end

      flash[:error] = 'Something went wrong with your last question. We have been notified.'
      respond_to do |format|
        format.html { redirect_to action: :index }
        format.json { render json: { error_status: 'BAD REQUEST', question_status: 'answered' }, status: 400 }
      end

    else

      ### Validate data provided by User ###
      if @current_questionable.is_valid?(params['dynamic_question_data'])
        ### If data provided is good ###
        @current_questionable.answer(current_user, params)

        ## TEST MODE: Extract result of last question and display as flash message ##
        # if new_dqd_able.is_a?(Result) && current_user.is_admin?
        #   flash[:warning] = "For the last challenge, you scored #{new_dqd_able.result_overview}"
        # end

        # Redirect to next question
        respond_to do |format|
          format.html { redirect_to action: :index }
          format.json { render json: { question_status: 'answered' }, status: 200 }
        end
      else

        # Temporary deactivation as is does not show properly
        # flash[:error] = 'You haven\'t answered this question correctly!'

        # NOTE: validation errors are available in @current_questionable.errors ActiveRecord errors.
        # title = "QuestionsController#save_question: Validation errors: Questionable: #{@current_questionable.class.name.to_s}##{@current_questionable.id.to_s} \nUser: #{current_user.id}"
        # msg = "============ Current Questionable ============\n"
        # msg += @current_questionable.to_yaml
        # msg += "\n\n============ Current Questionable -> errors ============\n"
        # msg += @current_questionable.errors.full_messages.to_yaml

        # Alert.questions(title, msg)

        respond_to do |format|
          format.html { render template: @current_questionable.layout.template_name }
          format.json { render json: { error_status: 'NOT VALID', question_status: 'answered', validation_errors: @current_questionable.errors }, status: 400 }
        end

      end
      nil
    end
  end

  def save_question_edge_cases(current_questionable, _questionable_string, _questionable_class, params)
    @current_questionable = current_questionable
    return_hash = {}

    case params['status']
      # when 'reported'
      #   ## TODO : Create a report object with the information stored in params['reportInfo']
      #   ## The reception is being reported, however we actually want the production to be reported.
      #   if @current_questionable.is_a?(Reception)
      #     recording = @current_questionable.production.recording
      #     if recording
      #       message = "Recording of production #{@current_questionable.production} has been reported."
      #       message += "\nListen to the recording here : #{@current_questionable.production.recording.data.url}"
      #       message += "\nConfirm the report by discarding the production here (must be loggued as admin) : "+discard_production_questions_url({:id => @current_questionable.production.id, :recording_id => @current_questionable.production.recording.id})
      #     else
      #       message = "Recording of production #{@current_questionable.production} has been reported."
      #       message = 'However the recording cannot be found'
      #     end

      #     Alert.questions('QuestionsController#save_question: A question has been reported', message)

      #     @current_questionable.production.reported!
      #   else
      #   end
      #   return_hash[:question_status] = 'reported'
      #   @current_questionable.reported!
      #   return_status = 200

    when 'timeout', 'leaving'
      return_hash[:question_status] = params['status']

      # Timeout management if user has entered some stuff
      if @current_questionable
        @current_questionable.answer(current_user, params)
        @current_questionable.timeout!
      end
      return_status = 200

    when 'skipped'
      return_hash[:question_status] = params['status']

      # Timeout management if user has entered some stuff
      if @current_questionable
        @current_questionable.answer(current_user, params)
        @current_questionable.skipped!
      end
      return_status = 200

    when 'no_status'
      return_hash[:error_status] = 'BAD REQUEST'
      return_hash[:question_status] = 'no_status'
      @current_questionable.technical_error!
      return_status = 400

    else
      return_hash[:question_status] = 'no_status'
      return_hash[:error_status] = 'BAD REQUEST'
      @current_questionable&.technical_error!

      begin
        msg = "Questionable: #{@current_questionable.class.name}##{@current_questionable.id} \nUser: #{current_user.id}"
        msg += "\n\n============ Current Questionable ============\n"
        msg += @current_questionable.to_yaml
        Alert.questions('QuestionsController#save_question: no_status', msg)
      rescue StandardError => e
        EventLog.add(current_user, :question, "QuestionsController#save_question: no_status: #{@current_questionable.to_json} - #{e.message}", :warning)
      end

      return_status = 400
    end

    respond_to do |format|
      format.html { redirect_to action: :index }
      format.json { render json: return_hash, status: return_status }
    end
    nil
  end
end
