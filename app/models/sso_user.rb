# == Schema Information
#
# Table name: api_users
#
#  id                                     :integer          not null, primary key
#  access_control_allow_methods           :string
#  access_control_allow_origin            :string
#  api_ti_creation_requires_rc            :boolean          default(FALSE)
#  authentication_token                   :string
#  authentication_token_expires_at        :datetime
#  days_before_test_instance_cancellation :integer
#  default_client_name                    :string
#  default_locale                         :string
#  default_sanitized_client_name          :text
#  default_test_language                  :string
#  deleted_at                             :datetime
#  force_api_user_brand                   :boolean          default(FALSE)
#  force_identity_check                   :boolean          default(FALSE)
#  force_test_mode                        :boolean          default(TRUE)
#  metadata                               :jsonb            not null
#  metadata_1                             :string           default({})
#  name                                   :string           not null
#  passphrase                             :string           not null
#  source                                 :integer
#  status                                 :integer          default("initialized"), not null
#  support_email                          :string
#  type                                   :string
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  default_test_profile_id                :integer
#  identity_provider_id                   :bigint
#
# Indexes
#
#  index_api_users_on_identity_provider_id  (identity_provider_id)
#
class SsoUser < ApiUser
  belongs_to :default_client_config, -> { where.not(client_configs: { sanitized_client_name: [nil, ''] }) }, class_name: 'ClientConfig', foreign_key: 'default_sanitized_client_name', :primary_key => 'sanitized_client_name'

  validates :name, uniqueness: true, presence: true
  validates :default_test_profile_id, presence: true
  validates :default_test_language, presence: true
  validates :source, presence: true

  after_initialize do
    self.status = ApiUser.statuses[:active]
  end

  def create_test_instance(first_name:, last_name:, email:, order_reference: nil, order_information: {})
    api_order = ApiOrder.create({
                                  api_user_id: id,
                                  order_reference: order_reference || SecureRandom.hex,
                                  order_information:,
                                  status: 0,
                                  source:
                                })

    ti_request = TestInstanceRequest.new({
                                           test_profile_id: default_test_profile_id,
                                           test_language: default_test_language,
                                           test_mode: force_test_mode,
                                           skip_email: true,
                                           certificate_sent_to_candidate: false
                                         }, {
                                           email:,
                                           first_name:,
                                           last_name:,
                                           phone_number: nil,
                                           locale: default_locale,
                                           group: name
                                         }, {
                                           source_type: :external_api,
                                           client_name: default_client_config&.client_name,
                                           source_id: api_order.id,
                                           client_type: SOURCE_CLIENT_TYPES[:idp]
                                         })
    user, _ti = TestInstance.create_with_user(ti_request)
    raise ActiveRecord::RecordInvalid, user unless user&.valid?

    user
  end

  def associated_user_with_email?(email)
    return false if email.blank?

    test_instances.joins(:user).exists?(users: { email: })
  end
end
