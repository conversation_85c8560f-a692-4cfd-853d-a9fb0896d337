# == Schema Information
#
# Table name: api_users
#
#  id                                     :integer          not null, primary key
#  access_control_allow_methods           :string
#  access_control_allow_origin            :string
#  api_ti_creation_requires_rc            :boolean          default(FALSE)
#  authentication_token                   :string
#  authentication_token_expires_at        :datetime
#  days_before_test_instance_cancellation :integer
#  default_client_name                    :string
#  default_locale                         :string
#  default_sanitized_client_name          :text
#  default_test_language                  :string
#  deleted_at                             :datetime
#  force_api_user_brand                   :boolean          default(FALSE)
#  force_identity_check                   :boolean          default(FALSE)
#  force_test_mode                        :boolean          default(TRUE)
#  metadata                               :jsonb            not null
#  metadata_1                             :string           default({})
#  name                                   :string           not null
#  passphrase                             :string           not null
#  source                                 :integer
#  status                                 :integer          default("initialized"), not null
#  support_email                          :string
#  type                                   :string
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  default_test_profile_id                :integer
#  identity_provider_id                   :bigint
#
# Indexes
#
#  index_api_users_on_identity_provider_id  (identity_provider_id)
#
#  id                              :integer          not null, primary key
#  access_control_allow_methods    :string
#  access_control_allow_origin     :string
#  api_ti_creation_requires_rc     :boolean          default(FALSE)
#  authentication_token            :string
#  authentication_token_expires_at :datetime
#  days_before_test_instance_cancellation :integer
#  default_locale                  :string
#  default_test_language           :string
#  deleted_at                      :datetime
#  force_api_user_brand            :boolean          default(FALSE)
#  force_identity_check            :boolean          default(FALSE)
#  force_test_mode                 :boolean          default(TRUE)
#  metadata                        :jsonb            not null
#  metadata_1                      :string           default({})
#  name                            :string           not null
#  passphrase                      :string           not null
#  status                          :integer          default("initialized"), not null
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  default_test_profile_id         :integer

class ApiUser < ApplicationRecord
  # Soft delete object
  acts_as_paranoid

  serialize :metadata_1, coder: JSON

  # ----------------------------------------
  # :section: Statuses
  # ----------------------------------------
  # Default is initialized (database setup)
  enum :status, {
    initialized: 0,
    active: 1,
    expired: 2
  }

  # Move through statuses
  def activate!; active!; end
  def expire!;   expired!; end

  # ----------------------------------------
  # :section: Callbacks
  # ----------------------------------------
  before_create :generate_passphrase
  before_create :generate_api_manager

  # ----------------------------------------
  # :section: Validations
  # ----------------------------------------
  validates :name, uniqueness: true, presence: true, length: { maximum: 50 }
  validates :days_before_test_instance_cancellation, numericality: { greater_than: 0, only_integer: true, allow_nil: true }

  # ----------------------------------------
  # :section: References
  # ----------------------------------------
  has_one :api_oauth_manager
  accepts_nested_attributes_for :api_oauth_manager
  has_many :api_orders
  has_many :test_instances
  has_many :remote_clients
  belongs_to :identity_provider

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :test_instance_cancellation_enabled, -> { where.not(days_before_test_instance_cancellation: nil) }

  def test_instances_to_cancel
    if days_before_test_instance_cancellation
      test_instances.not_test_mode.in_progress
                    .where(updated_at: ..days_before_test_instance_cancellation.days.ago)
                    .order(updated_at: :asc)
    else
      TestInstance.none
    end
  end
  # ----------------------------------------
  # :section: Delegation
  # ----------------------------------------
  delegate :call, to: :api_oauth_manager, allow_nil: true

  # ----------------------------------------
  # :section: Normalization
  # ----------------------------------------
  normalizes :default_locale, with: ->(default_locale) { default_locale.presence }
  normalizes :default_test_language, with: ->(default_test_language) { default_test_language.presence }

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------
  def self.authenticate(name, passphrase)
    api_user = ApiUser.find_by(name: name, passphrase: passphrase, status: ApiUser.statuses.fetch(:active))
    if api_user
      api_user.generate_authentication_token! if api_user.authentication_token_expires_at.nil? or Time.now > api_user.authentication_token_expires_at
      api_user
    else
      false
    end
  end

  # ----------------------------------------
  # :section: Instance methods
  # ----------------------------------------
  # List all conditions for an api user to be valid
  def is_usable?
    self.status == 'active' &&
      !self.passphrase.nil?
  end

  def has_cors_headers?
    !access_control_allow_origin.nil? &&
      !access_control_allow_origin.empty? &&
      !access_control_allow_methods.nil? &&
      !access_control_allow_methods.empty?
  end

  # ----------------------------------------
  # :section: Token management
  # ----------------------------------------
  # Generate a new authentication token
  def generate_authentication_token!
    loop do
      self.authentication_token = SecureRandom.urlsafe_base64(64)
      break unless ApiUser.find_by(authentication_token: authentication_token)
    end
    self.authentication_token_expires_at = Time.now + API_AUTHENTIFICATION_TOKEN_LIFETIME.hour
    save!
  end

  def generate_api_manager
    self.api_oauth_manager = ApiOauthManager.new
  end

  def touch_token!
    update(authentication_token_expires_at: (Time.now + API_AUTHENTIFICATION_TOKEN_LIFETIME.hour))
  end

  # Automatically generate passphrase at creation
  def generate_passphrase(force = false)
    if self.passphrase.nil? || force
      loop do
        self.passphrase = SecureRandom.urlsafe_base64(32)
        break unless ApiUser.find_by(passphrase: passphrase)
      end
    end
  end

  # Refresh passphrase
  def refresh_passphrase!
    generate_passphrase(true)
    save
  end
end
