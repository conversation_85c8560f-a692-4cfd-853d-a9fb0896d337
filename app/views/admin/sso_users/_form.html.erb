<h3>Configuration details</h3>
<%= form_section('Account credentials') %>
<%= form_text_field(f, :name) %>
<%= form_section('Test Mode') %>
<%= form_check_box(f, :force_test_mode) %>
<%= form_section('Default test values') %>
<%= form_select_with_prompt_field(f, :default_test_profile_id, TestProfile.order(:name), :id, :name) %>
<%= form_select_with_prompt_field(f, :default_test_language, AVAILABLE_TEST_LANGUAGES, :to_s, :to_s) %>
<%= form_select_with_prompt_field(f, :default_locale, Language.available, :to_s, :to_s, :default_locale, prompt: 'Browser locale', include_blank: 'Browser locale') %>
<%= form_select_with_prompt_field(f, :source, ApiOrder::SSO_SOURCES.map{|k, v| Struct.new(:name, :value).new(k, v) }, :value, :name) %>
<%= form_text_field(f, :default_client_name) %>
<%= form_section('Support Configuration') %>
<%= form_text_field(f, :support_email) %>
<% if @resource.persisted? %>
  <%= form_section('Timestamps') %>
  <%= form_text_field(f, :created_at, disable: true) %>
  <%= form_text_field(f, :updated_at, disable: true) %>
<% end %>
